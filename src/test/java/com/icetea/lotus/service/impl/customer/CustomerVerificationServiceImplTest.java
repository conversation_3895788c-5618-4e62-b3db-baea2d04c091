package com.icetea.lotus.service.impl.customer;

import com.icetea.lotus.constant.AuditStatus;
import com.icetea.lotus.constant.DocumentType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.CustomerVerificationRequest;
import com.icetea.lotus.dto.request.StatusUpdateRequest;
import com.icetea.lotus.dto.response.CustomerVerificationResponse;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberApplication;
import com.icetea.lotus.pagination.Criteria;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberApplicationService;
import com.icetea.lotus.service.customer.impl.CustomerVerificationServiceImpl;
import com.icetea.lotus.util.MessageResult;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.data.domain.*;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomerVerificationServiceImplTest {

    private CustomerVerificationRequest currentRequest;
    private CustomerVerificationServiceImpl customerVerificationService;

    @Mock
    private MemberApplicationService memberApplicationService;

    @Mock
    private LocaleMessageSourceService messageSource;

    @BeforeEach
    void setUp() {
        customerVerificationService = new CustomerVerificationServiceImpl(memberApplicationService, messageSource);

        // Setup mock data
        Member member = new Member();
        member.setEmail("<EMAIL>");
        member.setMobilePhone("1234567890");
        member.setRegistrationTime(Date.valueOf(LocalDate.now()));

        MemberApplication application = new MemberApplication();
        application.setRealName("John Doe");
        application.setMember(member);
        application.setAuditStatus(AuditStatus.AUDIT_SUCCESS);

        // Universal mock logic based on currentRequest
        when(memberApplicationService.findAll(any(Criteria.class), any(Pageable.class)))
                .thenAnswer(invocation -> {
                    Pageable pageable = PageRequest.of(0, 10);
                    if (currentRequest == null) return Page.empty(pageable);

                    boolean match = true;

                    if (currentRequest.getRealName() != null &&
                            !"John Doe".contains(currentRequest.getRealName())) match = false;

                    if (currentRequest.getEmail() != null &&
                            !"<EMAIL>".contains(currentRequest.getEmail())) match = false;

                    if (currentRequest.getMobilePhone() != null &&
                            !"1234567890".equals(currentRequest.getMobilePhone())) match = false;

                    if (currentRequest.getStatus() != null &&
                            !AuditStatus.AUDIT_SUCCESS.equals(currentRequest.getStatus())) match = false;

                    return match
                            ? new PageImpl<>(List.of(application), pageable, 1)
                            : Page.empty(pageable);
                });
    }

    @Test
    void testQueryPage_HappyPath_ReturnsWithCorrectData() {
        // Setup input
        PageModel pageModel = new PageModel();

        currentRequest = new CustomerVerificationRequest();
        currentRequest.setRealName("John");
        currentRequest.setEmail("<EMAIL>");
        currentRequest.setMobilePhone("1234567890");
        currentRequest.setStatus(AuditStatus.AUDIT_SUCCESS);

        // Execute
        MessageResult result = customerVerificationService.queryPage(pageModel, currentRequest);

        // Verify result object
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertInstanceOf(Map.class, result.getData());

        Map<String, Object> data = (Map<String, Object>) result.getData();
        assertNotNull(data);

        // Verify pagination metadata
        assertEquals(0, data.get("pageNumber"));
        assertEquals(10, data.get("pageSize"));
        assertEquals(1L, data.get("totalElements"));
        assertEquals(1, data.get("totalPages"));
        assertTrue((Boolean) data.get("last"));

        // Verify content
        List<CustomerVerificationResponse> content = (List<CustomerVerificationResponse>) data.get("content");
        assertNotNull(content);
        assertEquals(1, content.size());

        CustomerVerificationResponse response = content.get(0);
        assertEquals("John Doe", response.getRealName());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals("1234567890", response.getMobilePhone());
        assertNotNull(response.getRegistrationTime());
        assertEquals(AuditStatus.AUDIT_SUCCESS.getOrdinal(), response.getKycStatus());

        // Verify sort fallback
        assertEquals("id", pageModel.getProperty().get(0));
        assertEquals(Sort.Direction.DESC, pageModel.getDirection().get(0));
    }

    @Test
    void testDetailReturnsSuccessForIdCardWithBothImages() {
        // Arrange
        Long memberAppId = 1L;
        MemberApplication application = new MemberApplication();
        application.setRealName("Jane Doe");
        application.setAuditStatus(AuditStatus.AUDIT_SUCCESS);
        application.setDocumentType(DocumentType.ID_CARD);
        application.setIdentityCardImgFront("front.png");
        application.setIdentityCardImgReverse("back.png");
        application.setIdCard("ID123456");

        when(memberApplicationService.findOne(memberAppId)).thenReturn(application);

        // Act
        MessageResult result = customerVerificationService.detail(memberAppId);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertInstanceOf(CustomerVerificationResponse.class, result.getData());

        CustomerVerificationResponse response = (CustomerVerificationResponse) result.getData();
        assertEquals("Jane Doe", response.getRealName());
        assertEquals(DocumentType.ID_CARD, response.getDocumentType());
        assertEquals("front.png", response.getIdCardFront());
        assertEquals("back.png", response.getIdCardBack());
        assertEquals("ID123456", response.getIdCardNumber());
        assertEquals(AuditStatus.AUDIT_SUCCESS.getOrdinal(), response.getKycStatus());
        assertNull(response.getPassport());
    }

    @Test
    void testUpdateStatus_AuditSuccess_CallsAuditPassAndReturnsSuccess() {
        // Arrange
        Long id = 100L;
        MemberApplication application = new MemberApplication();
        application.setAuditStatus(AuditStatus.AUDIT_DEFEATED); // ensure different from target to trigger update

        when(memberApplicationService.findOne(id)).thenReturn(application);
        when(messageSource.getMessage("AUDIT_PASS")).thenReturn("AUDIT_PASS");

        StatusUpdateRequest req = new StatusUpdateRequest();
        req.setStatus(AuditStatus.AUDIT_SUCCESS.getOrdinal());

        // Act
        MessageResult result = customerVerificationService.updateStatus(id, req);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("AUDIT_PASS", result.getData());
        verify(memberApplicationService, times(1)).auditPass(application);
        verify(memberApplicationService, never()).auditFail(ArgumentMatchers.any());
    }

    @Test
    void testUpdateStatus_AuditDefeated_CallsAuditFailAndReturnsSuccess() {
        // Arrange
        Long id = 101L;
        MemberApplication application = new MemberApplication();
        application.setAuditStatus(AuditStatus.AUDIT_SUCCESS); // ensure different from target to trigger update

        when(memberApplicationService.findOne(id)).thenReturn(application);
        when(messageSource.getMessage("AUDIT_FAIL")).thenReturn("AUDIT_FAIL");

        StatusUpdateRequest req = new StatusUpdateRequest();
        req.setStatus(AuditStatus.AUDIT_DEFEATED.getOrdinal());

        // Act
        MessageResult result = customerVerificationService.updateStatus(id, req);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertEquals("AUDIT_FAIL", result.getData());
        verify(memberApplicationService, times(1)).auditFail(application);
        verify(memberApplicationService, never()).auditPass(ArgumentMatchers.any());
    }

    @Test
    void testDetailReturnsSuccessForPassportWithImage() {
        // Arrange
        Long id = 200L;
        MemberApplication application = new MemberApplication();
        application.setRealName("Passport User");
        application.setAuditStatus(AuditStatus.AUDIT_SUCCESS);
        application.setDocumentType(DocumentType.PASSPORT);
        application.setPassportImg("passport.png");

        when(memberApplicationService.findOne(id)).thenReturn(application);

        // Act
        MessageResult result = customerVerificationService.detail(id);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertInstanceOf(CustomerVerificationResponse.class, result.getData());

        CustomerVerificationResponse response = (CustomerVerificationResponse) result.getData();
        assertEquals(DocumentType.PASSPORT, response.getDocumentType());
        assertEquals("passport.png", response.getPassport());
        assertNull(response.getIdCardFront());
        assertNull(response.getIdCardBack());
        assertEquals(AuditStatus.AUDIT_SUCCESS.getOrdinal(), response.getKycStatus());
        assertEquals("Passport User", response.getRealName());
    }

    @Test
    void testDetail_NoDataWhenMemberApplicationMissing() {
        // Arrange
        Long id = 300L;
        when(memberApplicationService.findOne(id)).thenReturn(null);
        when(messageSource.getMessage("NO_DATA")).thenReturn("NO_DATA");

        // Act
        MessageResult result = customerVerificationService.detail(id);

        // Assert
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("NO_DATA", result.getMessage());
    }

    @Test
    void testDetail_NoDataWhenRequiredDocumentImagesMissing() {
        // Arrange
        Long id = 400L;
        MemberApplication passportMissingImg = new MemberApplication();
        passportMissingImg.setAuditStatus(AuditStatus.AUDIT_SUCCESS);
        passportMissingImg.setDocumentType(DocumentType.PASSPORT);
        passportMissingImg.setPassportImg(null); // missing image

        MemberApplication idCardMissingBack = new MemberApplication();
        idCardMissingBack.setAuditStatus(AuditStatus.AUDIT_SUCCESS);
        idCardMissingBack.setDocumentType(DocumentType.ID_CARD);
        idCardMissingBack.setIdentityCardImgFront("front.png");
        idCardMissingBack.setIdentityCardImgReverse(null); // missing back

        when(memberApplicationService.findOne(id)).thenReturn(passportMissingImg, idCardMissingBack);
        when(messageSource.getMessage("NO_DATA")).thenReturn("NO_DATA");

        // Act + Assert PASSPORT missing image
        MessageResult resultPassport = customerVerificationService.detail(id);
        assertNotNull(resultPassport);
        assertNotEquals(0, resultPassport.getCode());
        assertEquals("NO_DATA", resultPassport.getMessage());

        // Act + Assert ID_CARD missing back image
        MessageResult resultIdCard = customerVerificationService.detail(id);
        assertNotNull(resultIdCard);
        assertNotEquals(0, resultIdCard.getCode());
        assertEquals("NO_DATA", resultIdCard.getMessage());
    }

    @Test
    void testUpdateStatus_InvalidOrdinal_ReturnsInvalidStatusError() {
        // Arrange
        Long id = 500L;
        MemberApplication application = new MemberApplication();
        application.setAuditStatus(AuditStatus.AUDIT_DEFEATED);

        when(memberApplicationService.findOne(id)).thenReturn(application);
        when(messageSource.getMessage("INVALID_STATUS")).thenReturn("INVALID_STATUS");

        StatusUpdateRequest req = new StatusUpdateRequest();
        req.setStatus(99); // invalid ordinal

        // Act
        MessageResult result = customerVerificationService.updateStatus(id, req);

        // Assert
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("INVALID_STATUS", result.getMessage());
        verify(memberApplicationService, never()).auditPass(any());
        verify(memberApplicationService, never()).auditFail(any());
    }
}

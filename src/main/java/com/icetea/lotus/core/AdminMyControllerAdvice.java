package com.icetea.lotus.core;

import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice
@Slf4j
public class AdminMyControllerAdvice {

    /**
     * Handle access denied exceptions (e.g., when a user has insufficient permissions).
     *
     * @param ex the exception
     * @return the custom error response
     */
    @ResponseBody
    @ExceptionHandler(value = AccessDeniedException.class)
    public MessageResult handleAccessDeniedException(AccessDeniedException ex) {
        log.error("Access denied: {}", ex.getMessage());
        return MessageResult.error(HttpServletResponse.SC_FORBIDDEN, "Access Denied: Insufficient permissions!");
    }

    /**
     * Handle authentication-related exceptions (e.g., invalid login or missing authentication).
     *
     * @param ex the exception
     * @return the custom error response
     */
    @ResponseBody
    @ExceptionHandler(value = AuthenticationException.class)
    public MessageResult handleAuthenticationException(AuthenticationException ex) {
        log.error("Authentication failed: {}", ex.getMessage());
        return MessageResult.error(HttpServletResponse.SC_UNAUTHORIZED, "Authentication Failed: Please log in.");
    }

    /**
     * Handle generic exceptions for a fallback mechanism, if needed.
     *
     * @param ex the exception
     * @return the custom error response
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public MessageResult handleGenericException(Exception ex) {
        log.error("An unexpected error occurred: {}", ex.getMessage());
        return MessageResult.error(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "An unexpected error occurred.");
    }
}
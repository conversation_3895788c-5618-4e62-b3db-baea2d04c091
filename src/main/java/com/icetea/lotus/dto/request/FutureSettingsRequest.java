package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class FutureSettingsRequest {

    @NotNull(message = "Status is required")
    @Min(value = 0, message = "Status must be 0 or 1")
    @Max(value = 1, message = "Status must be 0 or 1")
    private Integer status;

    @NotNull(message = "Visible Status is required")
    @Min(value = 0, message = "Status must be 0 or 1")
    @Max(value = 1, message = "Status must be 0 or 1")
    private Integer visibleStatus;

    @NotNull(message = "Min Order Quantity is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Min Order Quantity must be > 0.")
    private BigDecimal minVolume;

    @NotNull(message = "Max Order Quantity is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Max Order Quantity must be > 0.")
    private BigDecimal maxVolume;

    @NotNull(message = "Min Order Price is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Min Order Price must be > 0.")
    private BigDecimal minPrice;

    @NotNull(message = "Max Order Price is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Max Order Price must be > 0.")
    private BigDecimal maxPrice;

    @NotNull(message = "Min Order Notional is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Min Order Notional must be > 0.")
    private BigDecimal minTotal;

    @NotNull(message = "Max Order Notional is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Max Order Notional must be > 0.")
    private BigDecimal maxTotal;

    @NotNull(message = "Taker Fee is required.")
    @DecimalMin(value = "0", inclusive = true, message = "Taker Fee must be between 0% and 100%.")
    @DecimalMax(value = "100", inclusive = true, message = "Taker Fee must be between 0% and 100%.")
    private BigDecimal takerFee;

    @NotNull(message = "Maker Fee is required.")
    @DecimalMin(value = "-100", inclusive = true, message = "Maker Fee must be between -100% and 100%.")
    @DecimalMax(value = "100", inclusive = true, message = "Maker Fee must be between -100% and 100%.")
    private BigDecimal makerFee;

    @NotNull(message = "Base Leverage Step is required.")
    @Min(value = 1, message = "Base Leverage Step must be > 0.")
    private Integer baseLeverageStep;

    @NotNull(message = "Liquidation Fee is required.")
    @DecimalMin(value = "0", inclusive = true, message = "Liquidation Fee must be between 0% and 100%.")
    @DecimalMax(value = "100", inclusive = true, message = "Liquidation Fee must be between 0% and 100%.")
    private BigDecimal liquidationFee;

    @NotNull(message = "Maintenance Margin is required.")
    @DecimalMin(value = "0", inclusive = true, message = "Maintenance Margin must be between 0% and 100%.")
    @DecimalMax(value = "100", inclusive = true, message = "Maintenance Margin must be between 0% and 100%.")
    private BigDecimal maintenanceMargin;

    private BigDecimal tickSize;

    private BigDecimal stepSize;
}


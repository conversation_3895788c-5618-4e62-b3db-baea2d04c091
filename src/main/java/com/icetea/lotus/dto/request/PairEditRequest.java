package com.icetea.lotus.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class PairEditRequest {

    @NotBlank(message = "Symbol is required")
    private String symbol;

    @Valid
    private PairGeneralInfoRequest pairGeneralInfo;

    @Valid
    private SpotSettingsRequest spotSettingsRequest;

    @Valid
    private FutureSettingsRequest futureSettingsRequest;
}

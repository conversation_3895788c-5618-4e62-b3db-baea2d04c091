package com.icetea.lotus.dto.request;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SpotOrderListRequest {
    private Long userId;
    private String orderId;
    private String baseAsset;
    private String orderStatus;
    private String orderType;
    private BigDecimal priceStart;
    private BigDecimal priceEnd;
    private Boolean isBot;
    private int pageNo;
    private int pageSize;
}

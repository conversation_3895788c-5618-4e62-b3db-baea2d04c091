package com.icetea.lotus.dto.request;

import com.icetea.lotus.constant.AdminWalletType;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class AdminWalletTransferRequest {

    @NotNull(message = "From wallet type is required")
    private AdminWalletType fromWalletType;

    @NotNull(message = "To wallet type is required")
    private AdminWalletType toWalletType;

    /**
     * Transfer currency id, e.g. USDT, BTC, ETH, etc.
     */
    @NotNull(message = "Coin id is required")
    private String coinId;

    @NotNull(message = "Amount is required")
    private BigDecimal amount;

    private String description;
}
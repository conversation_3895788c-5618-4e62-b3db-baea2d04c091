/*
 * Copyright(C) 2025
 * DepositScreenRequest.java, July 28,2025
 * namhm
 */
package com.icetea.lotus.dto.request;

import lombok.Data;
import java.time.OffsetDateTime;

/**
* DTO for receiving filter parameters from the deposit history search request
* <AUTHOR>
*/
@Data
public class DepositScreenRequest {
    private String username;

    private Long memberId;

    private String email;

    private OffsetDateTime startTime;

    private OffsetDateTime endTime;

    private Integer status;

    private String coinName;

    // Whether to export (0: No, 1: Yes)
    private Integer isOut;

    private int pageNo;

    private int pageSize;
}

package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.math.BigDecimal;
@Data
public class PairGeneralInfoRequest {

    @NotBlank(message = "Pair Display Name is required.")
    @Pattern(regexp = "^[A-Z]{1,20}$", message = "Invalid format")
    private String pairName;

    @NotNull(message = "Tick Size is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Tick Size must be greater than 0.")
    private BigDecimal tickSize;

    @NotNull(message = "Step Size is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Step Size must be greater than 0.")
    private BigDecimal stepSize;
}

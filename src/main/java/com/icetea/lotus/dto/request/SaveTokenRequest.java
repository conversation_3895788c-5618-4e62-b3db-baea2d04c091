package com.icetea.lotus.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class SaveTokenRequest {
    @NotBlank(message = "The token name field is required")
    private String tokenName;
    @NotBlank(message = "The token symbol field is required")
    private String tokenSymbol;
    @NotBlank(message = "The token image field is required")
    private String tokenImage;
    private BigDecimal circulationSupply;
    private BigDecimal maxSupply;
    private String tokenDescription;
    @NotEmpty(message = "Token contracts list cannot be empty")
    @Valid
    private List<TokenContractRequest> tokenContracts;
}
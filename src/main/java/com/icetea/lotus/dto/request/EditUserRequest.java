package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;

@Data
public class EditUserRequest {
    @NotNull(message = "UserId is required")
    private Long userId;

    @NotBlank(message = "Full name is required")
    @Size(max = 100, message = "Full name must not exceed 100 characters")
    @Pattern(regexp = "^[A-Za-z ]+$", message = "Full name can only contain alphabetic characters and spaces")
    private String fullName;

    @NotBlank(message = "Email is required")
    @Email(message = "Please enter a valid email address")
    private String email;

    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "^(0\\d{9,10}|\\+\\d{9,15})$",
            message = "Phone number must start with '0' (10-11 digits) or '+' followed by country code")
    private String phone;

    @NotBlank(message = "Role is required")
    private String role;

}

package com.icetea.lotus.dto.request;

import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.RealNameStatus;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
public class CustomerListRequest {
    private String id;

    private String username;

    private String email;

    private String mobilePhone;

    /**
     * KYC status: (012 NOT_CERTIFIED/AUDITING/VERIFIED)
     */
    private RealNameStatus realNameStatus;
    /**
     * 01 (Normal/Illegal)
     */
    private CommonStatus status;

    /**
     * Member registration time
     */
    private OffsetDateTime startTime;

    private OffsetDateTime endTime;
}

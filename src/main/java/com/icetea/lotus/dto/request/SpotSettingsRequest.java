package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpotSettingsRequest {

    @NotNull(message = "Status is required")
    @Min(value = 0, message = "Status must be 0 or 1")
    @Max(value = 1, message = "Status must be 0 or 1")
    private Integer status;

    @NotNull(message = "Visible Status is required")
    @Min(value = 0, message = "Status must be 0 or 1")
    @Max(value = 1, message = "Status must be 0 or 1")
    private Integer visibleStatus;

    // Min Order Quantity
    @NotNull(message = "Min Order Quantity is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Min Order Quantity must be > 0.")
    private BigDecimal minVolume;

    // Max Order Quantity
    @NotNull(message = "Max Order Quantity is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Max Order Quantity must be > 0.")
    private BigDecimal maxVolume;

    // Min Order Price
    @NotNull(message = "Min Order Price is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Min Order Price must be > 0.")
    private BigDecimal minPrice;

    // Max Order Price
    @NotNull(message = "Max Order Price is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Max Order Price must be > 0.")
    private BigDecimal maxPrice;

    // Min Order Notional
    @NotNull(message = "Min Order Notional is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Min Order Notional must be > 0.")
    private BigDecimal minTotal;

    // Max Order Notional
    @NotNull(message = "Max Order Notional is required.")
    @DecimalMin(value = "0", inclusive = false, message = "Max Order Notional must be > 0.")
    private BigDecimal maxTotal;

    // Taker Fee
    @NotNull(message = "Taker Fee is required.")
    @DecimalMin(value = "0", inclusive = true, message = "Taker Fee must be between 0% and 100%.")
    @DecimalMax(value = "100", inclusive = true, message = "Taker Fee must be between 0% and 100%.")
    private BigDecimal takerFee;

    // Maker Fee
    @NotNull(message = "Maker Fee is required.")
    @DecimalMin(value = "-100", inclusive = true, message = "Maker Fee must be between -100% and 100%.")
    @DecimalMax(value = "100", inclusive = true, message = "Maker Fee must be between -100% and 100%.")
    private BigDecimal makerFee;
}

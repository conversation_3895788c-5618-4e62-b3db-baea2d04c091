package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PairTradingStatusRequest {
    @NotBlank(message = "The symbol field is required")
    private String symbol;
    @NotNull(message = "The enable field is required")
    @Min(value = 0, message = "The enable field must be 0 or 1")
    @Max(value = 1, message = "The enable field must be 0 or 1")
    private Integer enable;
}

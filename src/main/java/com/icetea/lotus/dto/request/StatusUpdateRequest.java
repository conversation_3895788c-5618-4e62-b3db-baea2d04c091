package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class StatusUpdateRequest {
    @NotNull(message = "Status cannot be null")
    @Min(value = 1, message = "Status must be 1 or 2")
    @Max(value = 2, message = "Status must be 1 or 2")
    private Integer status;
}

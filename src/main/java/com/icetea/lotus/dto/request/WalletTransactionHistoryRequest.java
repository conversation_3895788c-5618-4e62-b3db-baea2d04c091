package com.icetea.lotus.dto.request;

import com.icetea.lotus.constant.AdminWalletType;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
public class WalletTransactionHistoryRequest {
    private AdminWalletType walletName;
    private String coinId;
    private Integer transactionType;
    private OffsetDateTime startTime;
    private OffsetDateTime endTime;
    private Integer pageNo;
    private Integer pageSize;
}

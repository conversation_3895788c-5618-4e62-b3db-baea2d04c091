package com.icetea.lotus.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AdminHistoryWithdrawRequest {
    private Long memberId;
    private String username;
    private String email;
    private String coinName;
    private Integer status;
    private String startTime;
    private String endTime;
    @Builder.Default()
    private int pageNo = 0;
    @Builder.Default()
    private int pageSize = 20;

}

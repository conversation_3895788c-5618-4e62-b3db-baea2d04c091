package com.icetea.lotus.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Generate account to send to email
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerateAccountRequest {
    private String username;
    @NotBlank(message = "is required")
    @Size(min = 8, message = "must be at least 8 characters long")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$")
    private String password;
}

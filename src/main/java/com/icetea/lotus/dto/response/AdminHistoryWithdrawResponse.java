package com.icetea.lotus.dto.response;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import com.icetea.lotus.constant.WithdrawStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelSheet(name = "Withdraw History")
public class AdminHistoryWithdrawResponse {
    @Excel(name = "User ID")
    private String id;
    @Excel(name = "Customer ID")
    private Long memberId;
    @Excel(name = "Username")
    private String username;
    @Excel(name = "email")
    private String email;
    @Excel(name = "Coin Name")
    private String coinName;
    @Excel(name = "Coin Unit")
    private String coinUnit;
    @Excel(name = "Coin URL")
    private String coinUrl;
    @Excel(name = "Fee")
    private String fee;
    @Excel(name = "Created At")
    private String createdAt;
    @Excel(name = "Amount")
    private BigDecimal amount;
    @Excel(name = "Status")
    private WithdrawStatus status;
    @Excel(name = "address")
    private String address;
    @Excel(name = "tx ID")
    private String txId;
    @Excel(name = "network")
    private String network;
    @Excel(name = "transactionNumber")
    private String transactionNumber;
}

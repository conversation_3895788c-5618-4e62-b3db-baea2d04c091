package com.icetea.lotus.dto.response;


import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class TokenDetailResponse {
    private String imageUrl;
    private String imageName;
    private BigDecimal circulationSupply;
    private BigDecimal maxSupply;
    private String description;
    private Boolean spotTradingStatus; // On / Off
    private Boolean futureTradingStatus; // On / Off
    private String tokenName;
    private String tokenSymbol;
    List<TokenContractResponse> contracts;
}

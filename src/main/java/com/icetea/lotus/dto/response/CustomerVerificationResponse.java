package com.icetea.lotus.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.icetea.lotus.constant.DocumentType;
import com.icetea.lotus.constant.VerifyDocType;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerVerificationResponse {
    private Long id;
    private String realName;
    private String firstName;
    private String lastName;
    private String idCardNumber;
    private String idCardFront;
    private String idCardBack;
    private String passport;
    private String email;
    private String mobilePhone;
    private Integer gender;
    private String address;
    private String registrationTime;
    private Integer kycStatus;
    private String nationality;
    private Date dateOfBirth;
    private Date expirationDate;
    private VerifyDocType verifyDocType;
}

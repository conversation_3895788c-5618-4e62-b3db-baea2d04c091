package com.icetea.lotus.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpotOrderDetailResponse {
    private String orderId;
    private Long orderTime;
    private String orderType;
    private String status;
    private String orderSide;
    private String pair;
    private BigDecimal orderPrice;
    private BigDecimal orderQuantity;
    private String filledQuantity;
    private Long userId;
    private String username;
    private String email;
}

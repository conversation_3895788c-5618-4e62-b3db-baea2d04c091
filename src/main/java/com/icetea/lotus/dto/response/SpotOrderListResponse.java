package com.icetea.lotus.dto.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class SpotOrderListResponse {
    private String orderId;
    private Long userId;
    private String realName;
    private String email;
    private String pair;
    private BigDecimal executedVolume;
    private String orderType;
    private String orderSide;
    private String orderStatus;
    private BigDecimal orderPrice;
    private Long orderTime;
}

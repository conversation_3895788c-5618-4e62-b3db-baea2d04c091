package com.icetea.lotus.dto.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
public class TokenListingResponse {
    private String iconUrl;
    private String tokenName;
    private String symbol;
    private Instant listingTime;
    private BigDecimal price;
    private BigDecimal volume24h;
    private Integer status; // 0: Active / 1: Delisted
    private Boolean spotTradingStatus; // On / Off
    private Boolean futureTradingStatus; // On / Off
}
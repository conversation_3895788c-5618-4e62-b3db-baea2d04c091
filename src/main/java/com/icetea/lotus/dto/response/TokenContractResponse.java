package com.icetea.lotus.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;

@Data
@Builder
public class TokenContractResponse {
    private String networkProtocol;
    private String contractAddress;
    private String name;
    private String symbol;
    private BigDecimal totalSupply;
    private int decimals;
    @JsonIgnore
    private BigInteger totalSupplyRaw;
}

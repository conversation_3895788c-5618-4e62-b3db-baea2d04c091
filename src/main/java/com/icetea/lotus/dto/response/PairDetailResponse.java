package com.icetea.lotus.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PairDetailResponse {
    // Pair detail general
    private String baseAsset;
    private String baseAssetName;
    private String quoteAsset;
    private String quoteAssetName;
    private String pairName;
    private OffsetDateTime createTime;
    private BigDecimal tickSize;
    private BigDecimal stepSize;

    // Pair detail of trading type
    private BigDecimal minVolume;
    private BigDecimal maxVolume;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private BigDecimal minTotal;
    private BigDecimal maxTotal;
    private BigDecimal takerFee;
    private BigDecimal makerFee;

    // spot
    private Integer spotTradingStatus;
    private Integer spotTradingVisible;

    // future
    private Integer baseLeverageStep;
    private BigDecimal liquidationFee;
    private Integer futureTradingStatus;
    private Integer futureTradingVisible;
    private BigDecimal maintenanceMarginRate;
}

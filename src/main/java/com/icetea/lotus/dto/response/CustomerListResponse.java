package com.icetea.lotus.dto.response;

import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.RealNameStatus;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
@Builder
public class CustomerListResponse {
    private String id;
    private String realName;
    private String username;
    private String email;
    private String mobilePhone;
    private RealNameStatus realNameStatus;
    private CommonStatus status;
    private OffsetDateTime registrationTime;
    private OffsetDateTime kycTime;
    private String parentId;
    private String inviteCode;
    private String tier;
    private Boolean spotTradingStatus;
    private Boolean futureTradingStatus;
}

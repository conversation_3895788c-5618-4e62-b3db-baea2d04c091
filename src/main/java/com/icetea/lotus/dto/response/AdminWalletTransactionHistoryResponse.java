package com.icetea.lotus.dto.response;

import com.icetea.lotus.constant.AdminTransactionStatus;
import lombok.Data;


import java.math.BigDecimal;
import java.time.Instant;

@Data
public class AdminWalletTransactionHistoryResponse {
    private String transactionNo;
    private String fromWalletType;
    private BigDecimal amount;
    private BigDecimal balance;
    private String toWalletType;
    private String transactionType;
    private String coinUnit;
    private String coinName;
    private String coinURL;
    private Long adminId;
    private String network;
    private String description;
    private AdminTransactionStatus status;
    private Instant createTime;
}

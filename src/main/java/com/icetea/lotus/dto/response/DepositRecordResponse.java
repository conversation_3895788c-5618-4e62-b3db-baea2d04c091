/*
 * Copyright(C) 2025
 * DepositRecordResponse.java, July 28,2025
 * namhm
 */
package com.icetea.lotus.dto.response;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.constant.WithdrawStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * DTO for returning deposit record details to the client
 * <AUTHOR>
 */
@Data
public class DepositRecordResponse {
    @Excel(name = "Record ID", order = 0)
    private String id;

    @Excel(name = "Customer ID", order = 1)
    private Long memberId;

    @Excel(name = "Coin Name", order = 4)
    private String coinName;

    @Excel(name = "Coin Unit", order = 5)
    private String coinUnit;

    @Excel(name = "Coin Icon URL", order = 6)
    private String coinIconUrl;

    @Excel(name = "Deposit Amount", order = 9)
    private BigDecimal amount;

    @Excel(name = "Frozen Amount", order = 10)
    private BigDecimal frozenAmount;

    @Excel(name = "Created At", order = 8)
    private Instant createdAt;

    @Excel(name = "Status", order = 7)
    private WithdrawStatus status;

    @Excel(name = "Network", order = 12)
    private String network;

    @Excel(name = "Transaction Number", order = 13)
    private String transactionNumber;

    @Excel(name = "Hash", order = 14)
    private String txHash;

    @Excel(name = "From Address", order = 11)
    private String fromAddress;

    @Excel(name = "Fire Block Status", order = 15)
    private String fireblockStatus;

    @Excel(name = "User name", order = 2)
    private String username;

    @Excel(name = "Email", order = 3)
    private String email;
}
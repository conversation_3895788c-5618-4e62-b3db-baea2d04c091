package com.icetea.lotus.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;

/**
 * Global response DTO for API responses.
 * This class is used to standardize the structure of API responses across the application.
 *
 * @param <T> The type of data contained in the response.
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FutureServiceResponse<T> {

    private int code;
    private String message;
    private T data;
    
    private LocalDateTime timestamp;

    /**
     * Creates a successful response with default success message.
     *
     * @param <T>  The type of data contained in the response.
     * @param data The data to be included in the response.
     * @return A FutureServiceResponse object with success status and provided data.
     */
    public static <T> FutureServiceResponse<T> success(T data) {
        return FutureServiceResponse.<T>builder()
                .code(HttpStatus.OK.value())
                .message("SUCCESS")
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * Creates a successful response with a custom message.
     *
     * @param <T>     The type of data contained in the response.
     * @param data    The data to be included in the response.
     * @param message A custom success message.
     * @return A FutureServiceResponse object with success status, provided data, and custom message.
     */
    public static <T> FutureServiceResponse<T> success(T data, String message) {
        return FutureServiceResponse.<T>builder()
                .code(HttpStatus.OK.value())
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * Creates an error response with a default error message.
     *
     * @param <T> The type of data contained in the response.
     * @return A FutureServiceResponse object with error status and default error message.
     */
    public static <T> FutureServiceResponse<T> error(String message) {
        return FutureServiceResponse.<T>builder()
                .code(HttpStatus.BAD_REQUEST.value())
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * Creates an error response with a custom error code and message.
     *
     * @param <T>    The type of data contained in the response.
     * @param code   The custom error code.
     * @param message A custom error message.
     * @return A FutureServiceResponse object with the specified error code, message, and current timestamp.
     */
    public static <T> FutureServiceResponse<T> error(int code, String message) {
        return FutureServiceResponse.<T>builder()
                .code(code)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }
}

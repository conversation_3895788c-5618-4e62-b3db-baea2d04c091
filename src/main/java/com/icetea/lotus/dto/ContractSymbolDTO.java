package com.icetea.lotus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO Future Contract Symbol
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractSymbolDTO {
    private Long id;
    private String symbol;
    private String baseSymbol;
    private String quoteSymbol;
    private Integer enable;
    private Integer sort;
    private BigDecimal fee;
    private BigDecimal leverageMin;
    private BigDecimal leverageMax;
    private BigDecimal defaultLeverage;
    private Integer baseLeverageStep;
    private BigDecimal minVolume;
    private BigDecimal maxVolume;
    private Integer priceScale;
    private Integer volumeScale;
    private String marginMode;
    private BigDecimal maintenanceMarginRate;
    private BigDecimal liquidationFeeRate;
    private BigDecimal initialMarginRate;
    private String name;
    private BigDecimal multiplier;
    private BigDecimal pricePrecision;
    private Integer sizePrecision;
    private BigDecimal volumePrecision;
    private BigDecimal fundingRateCoefficient;
    private BigDecimal maxFundingRate;
    private BigDecimal minFundingRate;
    private Integer fundingInterval;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private LocalDateTime expiryDate;
    private MoneyDTO minTradeAmount;
    private MoneyDTO maxTradeAmount;
    private BigDecimal takerFeeRate;
    private BigDecimal makerFeeRate;
    private Boolean visible;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private BigDecimal priceStep;
    private BigDecimal amountStep;
    private Object matchingAlgorithm;
}

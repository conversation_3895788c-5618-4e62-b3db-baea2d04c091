package com.icetea.lotus.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Value object representing a monetary value.
 * Provides methods for creating Money instances from various types,
 * performing arithmetic operations, and comparing values.
 */
@Data
@AllArgsConstructor
public class MoneyDTO implements Serializable, Comparable<MoneyDTO> {
    private static final long serialVersionUID = 1L;

    private BigDecimal value;

    public MoneyDTO() {
        this.value = BigDecimal.ZERO;
    }

    /**
     * Create a Money instance from a BigDecimal value
     *
     * @param value The BigDecimal value to convert
     * @return A new Money instance representing the value
     */
    public static MoneyDTO of(BigDecimal value) {
        if (value == null) {
            return new MoneyDTO();
        }
        // Ensure precision does not exceed 8 decimal places
        return new MoneyDTO(value.setScale(8, RoundingMode.HALF_UP));
    }

    /**
     * Create a Money instance from a String value
     *
     * @param value The String value to convert
     * @return A new Money instance representing the value
     */
    public static MoneyDTO of(String value) {
        if (value == null || value.isEmpty()) {
            return new MoneyDTO();
        }
        try {
            // Ensure precision does not exceed 8 decimal places
            return new MoneyDTO(new BigDecimal(value).setScale(8, RoundingMode.HALF_UP));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Cannot convert string to BigDecimal: " + value, e);
        }
    }

    /**
     * Create a Money instance from a double value
     *
     * @param value The double value to convert
     * @return A new Money instance representing the value
     */
    public static MoneyDTO of(double value) {
        try {
            // Ensure precision does not exceed 8 decimal places
            return new MoneyDTO(BigDecimal.valueOf(value).setScale(8, RoundingMode.HALF_UP));
        } catch (Exception e) {
            throw new IllegalArgumentException("Cannot convert double to BigDecimal: " + value, e);
        }
    }

    /**
     * Add another Money to this Money
     *
     * @param other The Money to add
     * @return A new Money instance representing the result
     */
    public MoneyDTO add(MoneyDTO other) {
        return new MoneyDTO(this.value.add(other.value).setScale(8, RoundingMode.HALF_UP));
    }

    /**
     * Subtract another Money from this Money
     *
     * @param other The Money to subtract
     * @return A new Money instance representing the result
     */
    public MoneyDTO subtract(MoneyDTO other) {
        return new MoneyDTO(this.value.subtract(other.value).setScale(8, RoundingMode.HALF_UP));
    }

    /**
     * Multiply this Money by a BigDecimal multiplier
     *
     * @param multiplier The BigDecimal to multiply by
     * @return A new Money instance representing the result
     */
    public MoneyDTO multiply(BigDecimal multiplier) {
        return new MoneyDTO(this.value.multiply(multiplier).setScale(8, RoundingMode.HALF_UP));
    }

    /**
     * Divide this Money by a BigDecimal divisor
     *
     * @param divisor      The BigDecimal to divide by
     * @param scale        The scale for the result
     * @param roundingMode The rounding mode for the result
     * @return A new Money instance representing the result
     */
    public MoneyDTO divide(BigDecimal divisor, int scale, RoundingMode roundingMode) {
        if (divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Cannot divide by null or zero");
        }
        // Ensure scale does not exceed 8 decimal places
        int actualScale = Math.min(scale, 8);
        return new MoneyDTO(this.value.divide(divisor, actualScale, roundingMode));
    }

    /**
     * Divide this Money by another Money
     *
     * @param divisor The Money to divide by
     * @return A new Money instance representing the result
     */
    public MoneyDTO divide(MoneyDTO divisor) {
        if (divisor == null || divisor.isZero()) {
            throw new IllegalArgumentException("Cannot divide by null or zero");
        }
        return new MoneyDTO(this.value.divide(divisor.getValue(), 8, RoundingMode.HALF_UP));
    }

    /**
     * Check if this Money is greater than another Money
     *
     * @param other The Money to compare with
     * @return true if this Money is greater than the other Money, false otherwise
     */
    public boolean isGreaterThan(MoneyDTO other) {
        if (other == null) {
            return true; // Any value is greater than null
        }
        return this.value.compareTo(other.value) > 0;
    }

    /**
     * Check if this Money is less than another Money
     *
     * @param other The Money to compare with
     * @return true if this Money is less than the other Money, false otherwise
     */
    public boolean isLessThan(MoneyDTO other) {
        if (other == null) {
            return false; // Any value is not less than null
        }
        return this.value.compareTo(other.value) < 0;
    }

    public boolean isZero() {
        return this.value.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * Check if this Money is greater than or equal to another Money
     *
     * @param other The Money to compare with
     * @return true if this Money is greater than or equal to the other Money, false otherwise
     */
    public boolean isGreaterThanOrEqual(MoneyDTO other) {
        if (other == null) {
            return true; // Any value is greater than or equal to null
        }
        return this.value.compareTo(other.value) >= 0;
    }

    /**
     * Check if this Money is less than or equal to another Money
     *
     * @param other The Money to compare with
     * @return true if this Money is less than or equal to the other Money, false otherwise
     */
    public boolean isLessThanOrEqual(MoneyDTO other) {
        if (other == null) {
            return false; // Any value is not less than or equal to null
        }
        return this.value.compareTo(other.value) <= 0;
    }

    /**
     * Negate the Money value
     *
     * @return A new Money instance with the negated value
     */
    public MoneyDTO negate() {
        return new MoneyDTO(this.value.negate().setScale(8, RoundingMode.HALF_UP));
    }

    /**
     * Convert Money to String representation
     *
     * @return String representation of the Money value
     */
    @Override
    public String toString() {
        return value.toPlainString();
    }


    /**
     * Check if the value exceeds NUMERIC(18,8) limits
     */
    @Override
    public int compareTo(MoneyDTO other) {
        if (other == null) {
            return 1; // Any value is greater than null
        }
        return this.value.compareTo(other.value);
    }
}

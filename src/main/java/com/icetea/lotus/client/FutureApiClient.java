package com.icetea.lotus.client;

import com.icetea.lotus.common.constants.SymbolStatus;
import com.icetea.lotus.dto.RequiredPnlSummaryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "${feign.client.future-api.name}")
public interface FutureApiClient {

    @GetMapping("/future-api/api/v1/admin/pnl-summary")
    ResponseEntity<RequiredPnlSummaryDto> getRequiredPnlAmount(@RequestParam String symbol);

    @PostMapping("/future-api/api/v1/admin/update-future-trade")
    ResponseEntity<String> updateFutureContractSymbol(@RequestParam String symbol, @RequestParam SymbolStatus symbolStatus);

}

package com.icetea.lotus.client;

import com.icetea.lotus.config.oauth2.OAuthFeignConfig;
import com.icetea.lotus.util.MessageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "${feign.client.external-api.name}", configuration = OAuthFeignConfig.class)
public interface ExternalApiClient {

    @PostMapping("/uc/member/admin-delete-account")
    MessageResult adminDeleteAccount(@RequestParam Long memberId);

    @PostMapping("/uc/member/cancel-all-spot-orders")
    MessageResult cancelAllSpotOrders(@RequestParam String symbol);
}

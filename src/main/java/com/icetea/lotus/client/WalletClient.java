package com.icetea.lotus.client;

import com.icetea.lotus.dto.request.AdminHistoryWithdrawRequest;
import com.icetea.lotus.dto.response.AdminHistoryWithdrawResponse;
import com.icetea.lotus.dto.TransferRecordDTO;
import com.icetea.lotus.vo.TransferRecordVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${feign.client.wallet.name}")
public interface WalletClient {

    @PostMapping("/wallet/api/v1/wallets/history/withdraw")
    Page<AdminHistoryWithdrawResponse> getAllWithdrawHistory(@RequestBody AdminHistoryWithdrawRequest request);

    @PostMapping("/wallet/api/v1/wallets/transfer/page-query")
    Page<TransferRecordVO> getTransferHistory(@RequestBody TransferRecordDTO screen);
}

package com.icetea.lotus.service.finance;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Withdraw;
import com.icetea.lotus.model.screen.WithdrawRecordScreen;
import com.icetea.lotus.model.screen.WithdrawScreen;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.BindingResult;

import java.io.IOException;

public interface ExtendedWithdrawService {
    MessageResult coinList();

    MessageResult protocolList();

    MessageResult pageQuery(PageModel pageModel, WithdrawScreen withdrawScreen, HttpServletResponse response) throws IOException;

    MessageResult merge(Withdraw withdraw, BindingResult bindingResult);

    MessageResult all();

    MessageResult pageQuery(PageModel pageModel, WithdrawRecordScreen screen);

    MessageResult detail(Long id);

    MessageResult auditPass(Long[] ids);

    MessageResult auditNoPass(Long[] ids);

    MessageResult addNumber(Long id, String transactionNumber);

    MessageResult remittance(Admin admin, Long[] ids, String transactionNumber, String password);

    MessageResult pageQuerySuper(PageModel pageModel, WithdrawRecordScreen screen, Long memberId);

    MessageResult getAllStatuses();
}

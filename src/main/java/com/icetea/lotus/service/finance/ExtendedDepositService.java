package com.icetea.lotus.service.finance;

import com.icetea.lotus.dto.request.DepositScreenRequest;
import com.icetea.lotus.dto.response.DepositRecordResponse;
import com.icetea.lotus.util.MessageResult;
import org.springframework.data.domain.Page;


import java.io.IOException;

public interface ExtendedDepositService {
    MessageResult coinList();

    MessageResult protocolList();

    Page<DepositRecordResponse> pageQuery(DepositScreenRequest depositScreenRequest) throws IOException;

    MessageResult getStatusList();
}

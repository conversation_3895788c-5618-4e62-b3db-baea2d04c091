package com.icetea.lotus.service.finance.impl;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.WithdrawStatus;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.CoinprotocolDTO;
import com.icetea.lotus.entity.spot.*;
import com.icetea.lotus.dto.request.DepositScreenRequest;
import com.icetea.lotus.dto.response.DepositRecordResponse;
import com.icetea.lotus.service.*;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedDepositService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * The type Extended deposit service.
 */
@Service
@Slf4j
public class ExtendedDepositServiceImpl extends BaseAdminController implements ExtendedDepositService {
    private final CoinService coinService;

    private final CoinprotocolService coinprotocolService;

    private final MemberService memberService;

    private final DepositRecordService depositRecordService;

    public ExtendedDepositServiceImpl(BaseAdminService baseAdminService, CoinService coinService, CoinprotocolService coinprotocolService,
                                      MemberService memberService,
                                      DepositRecordService depositRecordService) {
        super(baseAdminService);
        this.coinService = coinService;
        this.coinprotocolService = coinprotocolService;
        this.memberService = memberService;
        this.depositRecordService = depositRecordService;
    }


    @Override
    public MessageResult coinList() {
        List<CoinDTO> list = coinService.list();

        return success(list);
    }

    @Override
    public MessageResult protocolList() {
        List<CoinprotocolDTO> list = coinprotocolService.list();

        return success(list);
    }


    /**
     * PageQuery is the method used for handling to get deposit records with pagination, filter
     *
     * @param depositScreenRequest filter request and pagination
     * @return a message contains a list of deposit records with pagination
     */
    @Override
    public Page<DepositRecordResponse> pageQuery(DepositScreenRequest depositScreenRequest) throws IOException {
        log.info("Check deposit screen {}", depositScreenRequest);
        List<BooleanExpression> booleanExpressions = buildFilterBooleanExpression(depositScreenRequest);

        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);

        PageModel pageModel = new PageModel();
        pageModel.setPageNo(depositScreenRequest.getPageNo());
        pageModel.setPageSize(depositScreenRequest.getPageSize());
        log.info("Check set price {}", pageModel);

        Page<DepositRecord> all = depositRecordService.list(predicate, pageModel);

        List<Long> memberIds = all.getContent().stream().map(v -> v.getMember().getId()).toList();
        Map<Long, Member> memberMap = memberService.mapByMemberIds(memberIds);

        return all.map(v -> {
            DepositRecordResponse depositRecordResponse = new DepositRecordResponse();
            BeanUtils.copyProperties(v, depositRecordResponse);

            if(v.getProtocol() != null) {
                Coinprotocol coinprotocol = coinprotocolService.findByProtocol(v.getProtocol());
                if(coinprotocol != null) {
                    depositRecordResponse.setNetwork(coinprotocol.getProtocolname());
                }
            }

            if(v.getCoin() != null) {
                depositRecordResponse.setCoinName(v.getCoin().getName());
                depositRecordResponse.setCoinUnit(v.getCoin().getUnit());
                depositRecordResponse.setCoinIconUrl(v.getCoin().getIconUrl());
            }

            if(v.getMember() != null) {
                depositRecordResponse.setMemberId(v.getMember().getId());
                Long memberId = Long.valueOf(String.valueOf(depositRecordResponse.getMemberId()));
                if (memberMap.containsKey(memberId)) {
                    depositRecordResponse.setUsername(memberMap.get(memberId).getUsername());
                    depositRecordResponse.setEmail(memberMap.get(memberId).getEmail());
                }
            }
            return depositRecordResponse;
        });
    }

    /**
     * This method is used for mapping Deposit status into a list
     * @return a message contains a list of deposit transaction's status
     */
    @Override
    public MessageResult getStatusList() {
        Map<Integer, Object> statusDepositList = new HashMap<>();
        for(WithdrawStatus status : WithdrawStatus.values()) {
            statusDepositList.put(status.getOrdinal(), status.getName());
        }
        return success("Get list successfully", statusDepositList);
    }

    /**
     * buildFilterBooleanExpression is the method used for building criteria filter
     * @param depositScreenRequest filter request
     * @return a list contains deposit's screen filter
     */
    private List<BooleanExpression> buildFilterBooleanExpression(DepositScreenRequest depositScreenRequest) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        Long memberId = depositScreenRequest.getMemberId();
        try {
            if (memberId != null) {
                booleanExpressions.add(QDepositRecord.depositRecord.member.id.eq(memberId));
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("User Id must be valid number");
        }

        String username = depositScreenRequest.getUsername();
        if (!StringUtils.isBlank(username)) {
            booleanExpressions.add(QDepositRecord.depositRecord.member.username.containsIgnoreCase(username));
        }

        String email = depositScreenRequest.getEmail();
        if(!StringUtils.isBlank(email)) {
            booleanExpressions.add(QDepositRecord.depositRecord.member.email.containsIgnoreCase(email));
        }

        try {
            OffsetDateTime startDate = depositScreenRequest.getStartTime();
            if (startDate != null) {
                booleanExpressions.add(QDepositRecord.depositRecord.createdAt.goe(startDate.toInstant()));
            }

            OffsetDateTime endDate = depositScreenRequest.getEndTime();
            if (endDate != null) {
                booleanExpressions.add(QDepositRecord.depositRecord.createdAt.loe(endDate.toInstant()));
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException(
                    "Invalid date format. Please use ISO-8601 format like yyyy-MM-dd'T'HH:mm:ssXXX (e.g., 2025-08-13T00:00:00+07:00)"
            );
        }

        Integer status = depositScreenRequest.getStatus();
        if (status != null) {
            WithdrawStatus statusEnum = WithdrawStatus.values() [status];
            booleanExpressions.add(QDepositRecord.depositRecord.status.eq(statusEnum));
        }

        String coinName = depositScreenRequest.getCoinName();
        if (!StringUtils.isBlank(coinName)) {
            booleanExpressions.add(QDepositRecord.depositRecord.coin.name.containsIgnoreCase(coinName));
        }

        return booleanExpressions;
    }

}

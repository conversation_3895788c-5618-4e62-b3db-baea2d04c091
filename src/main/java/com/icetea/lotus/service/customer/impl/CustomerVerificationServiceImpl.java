package com.icetea.lotus.service.customer.impl;

import com.icetea.lotus.constant.AuditStatus;
import com.icetea.lotus.constant.DocumentType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.VerifyDocType;
import com.icetea.lotus.dto.request.CustomerVerificationRequest;
import com.icetea.lotus.dto.request.StatusUpdateRequest;
import com.icetea.lotus.dto.response.CustomerVerificationResponse;
import com.icetea.lotus.entity.spot.MemberApplication;
import com.icetea.lotus.pagination.Criteria;
import com.icetea.lotus.pagination.Restrictions;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberApplicationService;
import com.icetea.lotus.service.customer.CustomerVerificationService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerVerificationServiceImpl implements CustomerVerificationService {

    private final MemberApplicationService memberApplicationService;
    private final LocaleMessageSourceService messageSource;

    private static final String SYSTEM_ERROR_UNEXPECTED = "SYSTEM_ERROR_UNEXPECTED";
    private static final String NO_DATA = "NO_DATA";

    /**
     * Queries a paginated list of customer verification records based on the provided search criteria.
     *
     * @param pageModel the pagination model containing page size and number
     * @param request   the customer verification search criteria including filters for real name, email, mobile phone, status, etc.
     * @return MessageResult containing the paginated customer verification list
     */
    @Override
    public MessageResult queryPage(PageModel pageModel, CustomerVerificationRequest request) {
        try {
            // Set default sort if not provided
            if (pageModel.getProperty() == null || pageModel.getProperty().isEmpty()) {
                List<String> list = new ArrayList<>();
                list.add("id");
                List<Sort.Direction> directions = new ArrayList<>();
                directions.add(Sort.Direction.DESC);
                pageModel.setProperty(list);
                pageModel.setDirection(directions);
            }

            // Create criteria using your custom Criteria class
            Criteria<MemberApplication> criteria = new Criteria<>();

            // Add filtering conditions based on the request
            if (StringUtils.hasLength(request.getRealName())) {
                criteria.add(Restrictions.like("realName", "%" + request.getRealName() + "%", true));
            }

            if (StringUtils.hasLength(request.getEmail())) {
                criteria.add(Restrictions.like("member.email", "%" + request.getEmail() + "%", true));
            }

            if (StringUtils.hasLength(request.getMobilePhone())) {
                criteria.add(Restrictions.eq("member.mobilePhone", request.getMobilePhone(), true));
            }

            if (request.getStatus() != null) {
                criteria.add(Restrictions.eq("auditStatus", request.getStatus(), true));
            }

            // Use the existing service method
            Page<MemberApplication> page = memberApplicationService.findAll(criteria, pageModel.getPageable());
            List<CustomerVerificationResponse> responseList = new ArrayList<>();

            page.getContent().forEach(item ->
                    responseList.add(CustomerVerificationResponse.builder()
                            .id(item.getId())
                            .realName(item.getRealName())
                            .email(item.getMember().getEmail())
                            .mobilePhone(item.getMember().getMobilePhone())
                            .registrationTime(item.getMember().getRegistrationTime().toString())
                            .kycStatus(item.getAuditStatus().getOrdinal())
                            .build())
            );

            Map<String, Object> result = new LinkedHashMap<>();
            result.put("content", responseList);
            result.put("pageNumber", page.getNumber());
            result.put("pageSize", page.getSize());
            result.put("totalElements", page.getTotalElements());
            result.put("totalPages", page.getTotalPages());
            result.put("last", page.isLast());

            return MessageResult.success(result);

        } catch (Exception e) {
            log.error("Unexpected error occurred while querying customer verification page: {}", e.getMessage());
            return MessageResult.error(messageSource.getMessage(SYSTEM_ERROR_UNEXPECTED));
        }
    }

    /**
     * Retrieves detailed customer verification information by member application ID.
     *
     * @param id the ID of the member application record to retrieve details
     * @return MessageResult containing CustomerVerificationResponse with detailed KYC information, or error message if not found
     */
    @Override
    public MessageResult detail(Long id) {
        try {
            MemberApplication memberApplication = memberApplicationService.findOne(id);
            if (memberApplication == null) {
                return MessageResult.error(messageSource.getMessage(NO_DATA));
            }

            String idCardFront = memberApplication.getIdentityCardImgFront();
            String idCardBack = memberApplication.getIdentityCardImgReverse();
            String passport = memberApplication.getPassportImg();
            VerifyDocType documentType = memberApplication.getVerifyDocType();

            if (documentType == null) {
                log.error("Document type is null for member application ID: {}", id);
                return MessageResult.error(messageSource.getMessage(NO_DATA));
            }
            if (documentType == VerifyDocType.PASSPORT && (passport == null || passport.isEmpty())) {
                log.error("Passport image is null for member application ID: {}", id);
                return MessageResult.error(messageSource.getMessage(NO_DATA));
            }
            if (documentType == VerifyDocType.ID_CARD &&
                    (idCardFront == null || idCardFront.isEmpty() || idCardBack == null || idCardBack.isEmpty())) {
                log.error("ID Card images are null for member application ID: {}", id);
                return MessageResult.error(messageSource.getMessage(NO_DATA));
            }
            
            CustomerVerificationResponse response = CustomerVerificationResponse.builder()
                    .id(memberApplication.getId())
                    .realName(memberApplication.getRealName())
                    .firstName(memberApplication.getFirstName())
                    .lastName(memberApplication.getLastName())
                    .gender(memberApplication.getGender() == null ? null : memberApplication.getGender().getOrdinal())
                    .address(memberApplication.getAddress())
                    .nationality(memberApplication.getNationality())
                    .dateOfBirth(memberApplication.getBirthday())
                    .expirationDate(memberApplication.getExpirationDate())
                    .verifyDocType(memberApplication.getVerifyDocType())
                    .idCardNumber(memberApplication.getIdNum())
                    .idCardFront(idCardFront)
                    .idCardBack(idCardBack)
                    .passport(passport)
                    .kycStatus(memberApplication.getAuditStatus().getOrdinal())
                    .build();
            return MessageResult.success(response);
        } catch (Exception e) {
            log.error("Unexpected error occurred while querying customer verification detail: {}", e.getMessage(), e);
            return MessageResult.error(messageSource.getMessage(SYSTEM_ERROR_UNEXPECTED));
        }
    }

    /**
     * Updates the audit status (Approve/Reject) of a customer verification application.
     *
     * @param id      the ID of the member application record to update
     * @param request the status update request containing the new status
     * @return MessageResult indicating success or failure of the operation
     */
    @Override
    public MessageResult updateStatus(Long id, StatusUpdateRequest request) {
        try {
            MemberApplication memberApplication = memberApplicationService.findOne(id);
            if (memberApplication == null) {
                return MessageResult.error(messageSource.getMessage(NO_DATA));
            }
            AuditStatus statusToUpdate = AuditStatus.getByOrdinal(request.getStatus());
            if (statusToUpdate == null) {
                return MessageResult.error(messageSource.getMessage("INVALID_STATUS"));
            }
            if (memberApplication.getAuditStatus() == statusToUpdate) {
                return MessageResult.success(messageSource.getMessage("NO_UPDATE"));
            }
            if (statusToUpdate == AuditStatus.AUDIT_SUCCESS) {
                memberApplicationService.auditPass(memberApplication, memberApplication.getCountry());
                return MessageResult.success(messageSource.getMessage("AUDIT_PASS"));
            } else if (statusToUpdate == AuditStatus.AUDIT_DEFEATED) {
                memberApplicationService.auditFail(memberApplication);
                return MessageResult.success(messageSource.getMessage("AUDIT_FAIL"));
            } else {
                return MessageResult.error(messageSource.getMessage("INVALID_STATUS"));
            }
        } catch (Exception e) {
            log.error("Unexpected error occurred while updating customer verification status: ", e);
        }
        return MessageResult.error(messageSource.getMessage(SYSTEM_ERROR_UNEXPECTED));
    }
}

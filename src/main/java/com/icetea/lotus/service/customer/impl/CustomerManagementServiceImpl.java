package com.icetea.lotus.service.customer.impl;

import com.icetea.lotus.client.ExternalApiClient;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.CustomerListRequest;
import com.icetea.lotus.dto.response.CustomerListResponse;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.pagination.Criteria;
import com.icetea.lotus.pagination.Restrictions;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.customer.CustomerManagementService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Customer management service implement
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerManagementServiceImpl implements CustomerManagementService {
    private final MemberService memberService;
    private final LocaleMessageSourceService messageSource;
    private final ExternalApiClient externalApiClient;

    /**
     * Retrieves a paginated list of customers based on search criteria.
     * Supports filtering by ID, username, email, mobile phone, business status,
     * member status, and registration time range.
     *
     * @param pageModel the page model containing pagination information
     * @return MessageResult containing paginated customer data with metadata
     */
    @Override
    public MessageResult pageQuery(PageModel pageModel, CustomerListRequest request, HttpServletRequest httpServletRequest) {
        try {
            if (pageModel.getProperty() == null) {
                List<String> list = new ArrayList<>();
                list.add("registrationTime");
                List<Sort.Direction> directions = new ArrayList<>();
                directions.add(Sort.Direction.DESC);
                pageModel.setProperty(list);
                pageModel.setDirection(directions);
            }
            // Query criteria
            Criteria<Member> specification = buildQuery(request);

            Page<Member> page = memberService.pageQuery(specification, pageModel.getPageable());

            ZoneId zoneId = ZoneId.of(httpServletRequest.getHeader("X-Timezone"));

            List<CustomerListResponse> customerList = page.getContent().stream()
                    .map(member -> CustomerListResponse.builder()
                            .id(String.valueOf(member.getId()))
                            .realName(member.getRealName())
                            .username(member.getUsername())
                            .email(member.getEmail())
                            .mobilePhone(member.getMobilePhone())
                            .realNameStatus(member.getRealNameStatus())
                            .status(member.getStatus())
                            .registrationTime(convertToOffsetDateTime(member.getRegistrationTime(), zoneId))
                            .kycTime(convertToOffsetDateTime(member.getApplicationTime(), zoneId))
                            .parentId(String.valueOf(member.getInviterId()))
                            .inviteCode(member.getPromotionCode())
                            .tier(member.getMemberLevel() != null ? member.getMemberLevel().getName() : null)
                            .spotTradingStatus(member.getTransactionStatus() != null ? member.getTransactionStatus().isIs() : null)
                            .futureTradingStatus(member.getFutureTradingStatus())
                            .build())
                    .toList();

            // Create response with pagination info
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("content", customerList);
            result.put("pageNumber", page.getNumber());
            result.put("pageSize", page.getSize());
            result.put("totalElements", page.getTotalElements());
            result.put("totalPages", page.getTotalPages());
            result.put("last", page.isLast());

            return MessageResult.success(result);
        } catch (Exception e) {
            log.error("Error in pageQuery", e);
            return MessageResult.error("Failed to fetch customer data");
        }
    }

    /**
     * Converts a Date object to OffsetDateTime with the specified time zone.
     *
     * @param date   the Date object to convert
     * @param zoneId the time zone to use for the conversion
     * @return the converted OffsetDateTime object
     */
    private OffsetDateTime convertToOffsetDateTime(Date date, ZoneId zoneId) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        return OffsetDateTime.ofInstant(instant, zoneId);
    }

    /**
     * Builds a query criteria for filtering Member entities based on the provided request parameters.
     * Supports filtering by ID, username, email, mobile phone, KYC status, member status, and
     * registration time range with proper date boundary handling.
     *
     * @param request the customer list request containing filter criteria
     * @return Criteria<Member> specification for querying Member entities
     */
    private Criteria<Member> buildQuery(CustomerListRequest request) {
        final String REGISTRATION_TIME_FIELD = "registrationTime";

        Criteria<Member> specification = new Criteria<>();

        if (request.getId() != null) {
            specification.add(Restrictions.eq("id", request.getId(), true));
        }
        if (request.getUsername() != null && !request.getUsername().trim().isEmpty()) {
            specification.add(Restrictions.like("username", "%" + request.getUsername().trim() + "%", true));
        }

        if (request.getEmail() != null && !request.getEmail().trim().isEmpty()) {
            specification.add(Restrictions.like("email", "%" + request.getEmail().trim() + "%", true));
        }

        if (request.getMobilePhone() != null && !request.getMobilePhone().trim().isEmpty()) {
            specification.add(Restrictions.eq("mobilePhone", request.getMobilePhone().trim(), true));
        }

        if (request.getRealNameStatus() != null) {
            specification.add(Restrictions.eq("realNameStatus", request.getRealNameStatus(), true));
        }

        if (request.getStatus() != null) {
            specification.add(Restrictions.eq("status", request.getStatus(), true));
        }

        if (request.getStartTime() != null) {
            Instant start = request.getStartTime().toInstant();
            Timestamp timestamp = Timestamp.from(start);
            specification.add(Restrictions.gte(REGISTRATION_TIME_FIELD, timestamp, true));
        }

        if (request.getEndTime() != null) {
            Instant end = request.getEndTime().toInstant();
            Timestamp timestamp = Timestamp.from(end);
            specification.add(Restrictions.lte(REGISTRATION_TIME_FIELD, timestamp, true));
        }

        return specification;
    }

    /**
     * Call to external in order to update status
     *
     * @param id : customer id
     * @return MessageResult : success or error
     */
    @Override
    public MessageResult updateTradingStatus(Long id, Boolean spotTradingStatus, Boolean futureTradingStatus) {
        //check request data
        if (id == null) {
            log.error("id must not be null");
            return MessageResult.error(messageSource.getMessage("REQUEST_FAILED"));
        }

        if (spotTradingStatus == null && futureTradingStatus == null) {
            log.warn("No trading status flag provided for customerId={}", id);
            return MessageResult.error(messageSource.getMessage("NO_UPDATE"));
        }

        //find user by id
        Member member = memberService.findOne(id);
        if (member == null) {
            return MessageResult.error(messageSource.getMessage("CUSTOMER_NOT_FOUND", new Object[]{id}));
        }

        String message;
        if (spotTradingStatus != null) {
            member.setTransactionStatus(spotTradingStatus ? BooleanEnum.IS_TRUE : BooleanEnum.IS_FALSE);
            message = spotTradingStatus
                    ? messageSource.getMessage("MEMBER_TRADING_ENABLED", new Object[]{"Spot trading"})
                    : messageSource.getMessage("MEMBER_TRADING_DISABLED", new Object[]{"Spot trading"});
            log.info("Updated spot trading status for memberId {}: {}", id, spotTradingStatus);
        } else {
            member.setFutureTradingStatus(futureTradingStatus);
            message = futureTradingStatus
                    ? messageSource.getMessage("MEMBER_TRADING_ENABLED", new Object[]{"Future trading"})
                    : messageSource.getMessage("MEMBER_TRADING_DISABLED", new Object[]{"Future trading"});
            log.info("Updated future trading status for memberId {}: {}", id, futureTradingStatus);
        }

        memberService.save(member);

        return MessageResult.success(message);
    }

    /**
     * Unblocks the customer with the given ID by enabling their sign-in ability.
     *
     * @param id the ID of the customer to unblock
     * @return a MessageResult indicating success or an error if the customer is not found
     */
    @Override
    public MessageResult unblockCustomer(Long id) {

        Member member = memberService.findOne(id);
        if (member == null) {
            return MessageResult.error(messageSource.getMessage("CUSTOMER_NOT_FOUND", new Object[]{id}));
        } else if (member.getStatus().equals(CommonStatus.TERMINATED)) {
            return MessageResult.error("Customer already has been terminated");
        }

        member.setStatus(CommonStatus.NORMAL);
        memberService.save(member);

        return MessageResult.success();
    }

    /**
     * Handling block user & update status
     *
     * @param userId : customer id
     * @return MessageResult
     */
    @Override
    public MessageResult blockCustomer(Long userId) {
        Member member = memberService.findOne(userId);
        if (member == null) {
            return MessageResult.error("Customer not found" + " " + userId);
        }

        if (member.getStatus().equals(CommonStatus.TERMINATED)) {
            return MessageResult.error("Customer already has been terminated");
        }

        member.setStatus(CommonStatus.ILLEGAL);
        memberService.save(member);

        return MessageResult.success("Customer banned successfully");
    }

    /**
     * Deletes a customer by their member ID.
     *
     * @param memberId the ID of the customer to delete
     * @return a MessageResult indicating the result of the operation
     */
    @Override
    public MessageResult deleteCustomer(Long memberId) {
        try {
            MessageResult messageResult = externalApiClient.adminDeleteAccount(memberId);
            if (messageResult.getCode() != 0) {
                log.error("Failed to delete customer with ID: {}. Error: {}", memberId, messageResult.getMessage());
                return MessageResult.error("Failed to delete customer: " + messageResult.getMessage());
            }
            return MessageResult.success("Customer deleted successfully");
        } catch (Exception e) {
            log.error("Error deleting customer with ID: {}", memberId, e);
            return MessageResult.error("Failed to delete customer");
        }
    }
}

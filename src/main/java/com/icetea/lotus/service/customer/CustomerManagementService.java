package com.icetea.lotus.service.customer;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.CustomerListRequest;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;

public interface CustomerManagementService {
    MessageResult pageQuery(PageModel pageModel, CustomerListRequest request, HttpServletRequest httpServletRequest);

    MessageResult updateTradingStatus(Long id, Boolean spotTradingStatus, Boolean futureTradingStatus);

    MessageResult blockCustomer(Long userId);

    MessageResult unblockCustomer(Long id);

    MessageResult deleteCustomer(Long memberId);
}

package com.icetea.lotus.service.customer;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.CustomerVerificationRequest;
import com.icetea.lotus.dto.request.StatusUpdateRequest;
import com.icetea.lotus.util.MessageResult;

public interface CustomerVerificationService {
    MessageResult queryPage(PageModel pageModel, CustomerVerificationRequest request);

    MessageResult detail(Long id);

    MessageResult updateStatus(Long id, StatusUpdateRequest request);
}

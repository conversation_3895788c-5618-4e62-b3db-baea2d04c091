package com.icetea.lotus.service.transaction.impl;

import com.icetea.lotus.client.WalletClient;
import com.icetea.lotus.dto.TransferRecordDTO;
import com.icetea.lotus.dto.request.AdminHistoryWithdrawRequest;
import com.icetea.lotus.dto.response.AdminHistoryWithdrawResponse;
import com.icetea.lotus.service.transaction.TransactionManagementService;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.vo.TransferRecordVO;
import feign.FeignException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionManagementServiceImpl implements TransactionManagementService {
    private final WalletClient walletClient;

    private int page = 0;
    private static final int SIZE = 100;

    @Override
    public Page<AdminHistoryWithdrawResponse> getAllWithdrawHistory(AdminHistoryWithdrawRequest adminHistoryWithdrawRequest) {
        try {
            return walletClient.getAllWithdrawHistory(adminHistoryWithdrawRequest);
        } catch (FeignException e) {
            log.error("Error fetching withdraw history: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to fetch withdraw history", e);
        }
    }

    @Override
    public Page<TransferRecordVO> getAllTransferHistory(TransferRecordDTO request) {
        try {
            return walletClient.getTransferHistory(request);
        } catch (FeignException e) {
            log.error("Error fetching transfer history: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to fetch transfer history", e);
        }
    }

    @Override
    public void exportWithdrawHistory(AdminHistoryWithdrawRequest screen, HttpServletResponse response) {
        try {
            page = 0;
            List<AdminHistoryWithdrawResponse> listAll = new ArrayList<>();
            Page<AdminHistoryWithdrawResponse> responseData;

            do {
                screen.setPageNo(page);
                screen.setPageSize(SIZE);

                responseData = walletClient.getAllWithdrawHistory(screen);
                log.info("Data response {}", responseData.getContent());
                listAll.addAll(responseData.getContent());

                page++;
            } while (page < responseData.getTotalPages());
            log.info("Check TotalPages {}", responseData.getTotalPages());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=withdraw-history.xlsx");

            ExcelUtil.listToExcel(listAll, AdminHistoryWithdrawResponse.class.getDeclaredFields(), response.getOutputStream());
        } catch (FeignException | IOException e) {
            log.error("Error export withdraw history: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to fetch withdraw history", e);
        }
    }

    @Override
    public void exportTransferHistory(TransferRecordDTO screen, HttpServletResponse response) {
        try {
            page = 0;

            Page<TransferRecordVO> responseData;
            List<TransferRecordVO> listAll = new ArrayList<>();
            do{
                screen.setPageNo(page);
                screen.setPageSize(SIZE);

                responseData = walletClient.getTransferHistory(screen);
                listAll.addAll(responseData.getContent());

                page++;
            } while(page < responseData.getTotalPages());

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=transfer-history.xlsx");

            ExcelUtil.listToExcel(listAll, TransferRecordVO.class.getDeclaredFields(), response.getOutputStream());

        } catch (FeignException | IOException e) {
            log.error("Error export transfer history: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to fetch transfer history", e);
        }
    }
}

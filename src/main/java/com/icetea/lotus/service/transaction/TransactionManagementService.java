package com.icetea.lotus.service.transaction;

import com.icetea.lotus.dto.TransferRecordDTO;
import com.icetea.lotus.dto.request.AdminHistoryWithdrawRequest;
import com.icetea.lotus.dto.response.AdminHistoryWithdrawResponse;
import com.icetea.lotus.vo.TransferRecordVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;


public interface TransactionManagementService {

    Page<AdminHistoryWithdrawResponse> getAllWithdrawHistory(AdminHistoryWithdrawRequest adminHistoryWithdrawRequest);

    Page<TransferRecordVO> getAllTransferHistory(TransferRecordDTO request);

    void exportWithdrawHistory(AdminHistoryWithdrawRequest adminHistoryWithdrawRequest, HttpServletResponse response) ;

    void exportTransferHistory(TransferRecordDTO screen, HttpServletResponse response) ;
}

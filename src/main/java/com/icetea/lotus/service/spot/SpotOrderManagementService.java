package com.icetea.lotus.service.spot;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.SpotOrderListRequest;
import com.icetea.lotus.util.MessageResult;

public interface SpotOrderManagementService {
    MessageResult pageQuery(SpotOrderListRequest spotOrderListRequest);

    MessageResult getOrderDetail(String orderId);

    MessageResult getListOrderStatus();

    MessageResult getListOrderType();

    MessageResult getBotTransaction();

    MessageResult getOrderTrades(String orderId, PageModel pageModel);
}

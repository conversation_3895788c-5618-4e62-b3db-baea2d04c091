package com.icetea.lotus.service.spot.impl;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dao.ExchangeOrderDetailRepository;
import com.icetea.lotus.dao.MemberDao;
import com.icetea.lotus.dto.request.SpotOrderListRequest;
import com.icetea.lotus.dto.response.SpotOrderDetailResponse;
import com.icetea.lotus.dto.response.SpotOrderListResponse;
import com.icetea.lotus.entity.ExchangeOrderDetail;
import com.icetea.lotus.entity.ExchangeOrderResource;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.entity.spot.ExchangeOrder;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.common.constants.SpotOrderStatus;
import com.icetea.lotus.pagination.Criteria;
import com.icetea.lotus.pagination.Restrictions;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.spot.SpotOrderManagementService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class SpotOrderManagementServiceImpl implements SpotOrderManagementService {

    private final ExchangeOrderService orderService;

    private final MemberDao memberDao;

    private final ExchangeOrderDetailRepository exchangeOrderDetailRepository;

    private final LocaleMessageSourceService messageSource;


    @Override
    public MessageResult pageQuery(SpotOrderListRequest request) {

        Criteria<ExchangeOrder> specification = new Criteria<>();

        if (request.getUserId() != null) {
            specification.add(
                    Restrictions.eq("memberId", request.getUserId(), true)
            );
        }

        if (request.getOrderId() != null) {
            specification.add(Restrictions.like("orderId", "%" + request.getOrderId() + "%", true));
        }

        if (request.getBaseAsset() != null) {
            specification.add(Restrictions.eq("coinSymbol", request.getBaseAsset(), true));
        }

        if (request.getOrderType() != null) {
            int ordinal = ExchangeOrderType
                    .fromString(request.getOrderType())
                    .ordinal();

            specification.add(Restrictions.eq("type", ordinal, true));
        }

        if (request.getPriceStart() != null) {
            specification.add(Restrictions.gte("price", request.getPriceStart(), true));
        }

        if (request.getPriceEnd() != null) {
            specification.add(Restrictions.lte("price", request.getPriceEnd(), true));
        }

        if (request.getIsBot() != null) {
            specification.add(
                    Boolean.TRUE.equals(request.getIsBot())
                            ? Restrictions.eq("orderResource", ExchangeOrderResource.ROBOT.ordinal(), true)
                            : Restrictions.ne("orderResource", ExchangeOrderResource.ROBOT.ordinal(), true)
            );
        }

        if (request.getOrderStatus() != null && !request.getOrderStatus().isBlank()) {
            SpotOrderStatus status = SpotOrderStatus.fromDisplayName(request.getOrderStatus());
            int orderStatus = ExchangeOrderStatus.valueOf(status.name()).ordinal();
            specification.add(Restrictions.eq("status", orderStatus, true));
        }

        PageModel pageModel = new PageModel();
        pageModel.setPageNo(request.getPageNo() > 0 ? request.getPageNo() : pageModel.getPageNo());
        pageModel.setPageSize(request.getPageSize() > 0 ? request.getPageSize() : pageModel.getPageSize());


        pageModel.setProperty(List.of("time"));
        pageModel.setDirection(List.of(Sort.Direction.DESC));

        Page<ExchangeOrder> page = orderService.pageQuery(specification, pageModel.getPageable());

        Page<SpotOrderListResponse> dtoList = page.map(exchangeOrder -> {
            Member member = memberDao.findById(exchangeOrder.getMemberId()).orElseThrow(
                    () -> new RuntimeException("Member not found for id: " + request.getUserId())
            );

            return SpotOrderListResponse.builder()
                    .userId(exchangeOrder.getMemberId())
                    .orderId(exchangeOrder.getOrderId())
                    .realName(Objects.toString(member.getRealName(), "_"))
                    .email(member.getEmail())
                    .pair(exchangeOrder.getSymbol())
                    .executedVolume(exchangeOrder.getTradedAmount().setScale(8, RoundingMode.DOWN))
                    .orderType(exchangeOrder.getType().name())
                    .orderSide(exchangeOrder.getDirection().name())
                    .orderPrice(exchangeOrder.getPrice())
                    .orderTime(exchangeOrder.getTime())
                    .orderStatus(SpotOrderStatus.valueOf(
                                    exchangeOrder.getStatus().name())
                            .getDisplayName())
                    .build();
        });
        return MessageResult.success(dtoList);
    }

    @Override
    public MessageResult getOrderDetail(String orderId) {
        ExchangeOrder exchangeOrder = orderService.findOne(orderId);
        if (exchangeOrder == null) {
            return MessageResult.error("Order not found");
        }
        Member member = memberDao.findMemberById(exchangeOrder.getMemberId());
        if (member == null) {
            return MessageResult.error("Member not found for order: " + orderId);
        }

        SpotOrderDetailResponse response = SpotOrderDetailResponse.builder()
                .orderId(exchangeOrder.getOrderId())
                .orderTime(exchangeOrder.getTime())
                .orderType(exchangeOrder.getType().name())
                .status(SpotOrderStatus.valueOf(exchangeOrder.getStatus().name()).getDisplayName())
                .orderSide(exchangeOrder.getDirection().name())
                .pair(exchangeOrder.getSymbol())
                .orderPrice(exchangeOrder.getPrice())
                .orderQuantity(exchangeOrder.getAmount())
                .filledQuantity(exchangeOrder.getTradedAmount().toPlainString())
                .userId(member.getId())
                .username(member.getUsername())
                .email(member.getEmail())
                .build();

        return MessageResult.success(response);
    }

    /**
     * Retrieves all trades associated with a specific spot order.
     *
     * @param orderId   the unique identifier of the spot order
     * @param pageModel the pagination parameters including page number and size
     * @return MessageResult containing the order's trade history
     */
    @Override
    public MessageResult getOrderTrades(String orderId, PageModel pageModel) {
        try {
            if (pageModel.getProperty() == null) {
                List<String> list = new ArrayList<>();
                list.add("time");
                List<Sort.Direction> directions = new ArrayList<>();
                directions.add(Sort.Direction.DESC);
                pageModel.setProperty(list);
                pageModel.setDirection(directions);
            }
            Page<ExchangeOrderDetail> trades = exchangeOrderDetailRepository.findAllByOrderId(orderId, pageModel.getPageable());
            if (trades.isEmpty()) {
                return MessageResult.error(messageSource.getMessage("NO_TRADES_FOUND", new Object[]{orderId}));
            }

            Map<String, Object> result = new LinkedHashMap<>();
            result.put("content", trades.getContent());
            result.put("pageNumber", trades.getPageable().getPageNumber());
            result.put("pageSize", trades.getPageable().getPageSize());
            result.put("totalElements", trades.getTotalElements());
            result.put("totalPages", trades.getTotalPages());
            result.put("last", trades.isLast());

            return MessageResult.success(result);
        } catch (Exception e) {
            return MessageResult.error(messageSource.getMessage("SYSTEM_ERROR_UNEXPECTED"));
        }
    }

    @Override
    public MessageResult getListOrderStatus() {
        Map<Integer, Object> orderStatusList = new HashMap<>();
        for (SpotOrderStatus status : SpotOrderStatus.values()) {
            orderStatusList.put(status.ordinal(), status.getDisplayName());
        }
        return MessageResult.success("Get order status list successfully", orderStatusList);
    }

    @Override
    public MessageResult getListOrderType() {
        Map<Integer, Object> orderTypeList = new HashMap<>();
        for (ExchangeOrderType status : ExchangeOrderType.values()) {
            orderTypeList.put(status.ordinal(), status.toString());
        }
        return MessageResult.success("Get order type list successfully", orderTypeList);
    }

    @Override
    public MessageResult getBotTransaction() {
        Map<Integer, Object> botTransactionList = new HashMap<>();
        for (ExchangeOrderResource botTransaction : ExchangeOrderResource.values()) {
            botTransactionList.put(botTransaction.ordinal(), botTransaction.toString());
        }
        return MessageResult.success("Get bot transaction status successfully", botTransactionList);
    }


}

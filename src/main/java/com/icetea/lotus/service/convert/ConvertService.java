package com.icetea.lotus.service.convert;

import com.icetea.lotus.constant.PageModel;

import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.ConvertCoin;
import com.icetea.lotus.model.screen.ConvertOrderScreen;
import com.icetea.lotus.util.MessageResult;
import org.springframework.validation.BindingResult;


public interface ConvertService {
    MessageResult create(ConvertCoin convertCoin);

    MessageResult update(ConvertCoin convertCoin, Admin admin, BindingResult bindingResult);

    MessageResult detail( String coinUnit);

    MessageResult pageQuery(PageModel pageModel);

    MessageResult pageQueryOrder(PageModel pageModel, ConvertOrderScreen screen);
}

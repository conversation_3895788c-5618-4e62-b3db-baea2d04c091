package com.icetea.lotus.service.notification.impl;

import com.icetea.lotus.service.notification.EmailService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Send email service implement
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmailServiceImpl implements EmailService {

    private final JavaMailSender javaMailSender;

    @Value("${spring.mail.username}")
    private String from;
    @Value("${spark.system.host}")
    private String host;
    @Value("${spark.system.name}")
    private String company;

    private static final String TEMPLATE_PATH = "/templates";

    private static final String LOGIN_URL = "https://bitcello.dev-glyph.click/en/login";

    public EmailServiceImpl(JavaMailSender javaMailSender) {
        this.javaMailSender = javaMailSender;
    }


    @Async
    public void sendEmailMsg(String email, String msg, String subject) throws MessagingException, IOException, TemplateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = null;
        helper = new MimeMessageHelper(mimeMessage, true);
        helper.setFrom(from);
        helper.setTo(email);
        helper.setSubject(company + "-" + subject);
        Map<String, Object> model = new HashMap<>(16);
        model.put("msg", msg);
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
        cfg.setClassForTemplateLoading(this.getClass(), TEMPLATE_PATH);
        Template template = cfg.getTemplate("simpleMessage.ftl");
        String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        helper.setText(html, true);

        // Send an email
        javaMailSender.send(mimeMessage);
        log.info("send email for {},content:{}", email, html);
    }

    public void sendEmailMsgAddNewUser(String username, String tempPassword, String email) throws MessagingException, IOException, TemplateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = null;
        helper = new MimeMessageHelper(mimeMessage, true);
        helper.setFrom(from);
        helper.setTo(email);
        Map<String, Object> model = new HashMap<>(16);
        model.put("username", username);
        model.put("tempPassword", tempPassword);
        model.put("loginUrl", LOGIN_URL);
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
        cfg.setClassForTemplateLoading(this.getClass(), TEMPLATE_PATH);
        Template template = cfg.getTemplate("sendToEmail.ftl");
        String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        helper.setText(html, true);

        //Send an email
        javaMailSender.send(mimeMessage);
    }

    @Async
    public void sendLockUserStatusEmail(String username, String accountId, String email, boolean lock) throws MessagingException, TemplateException, IOException {
        Map<String, Object> model = new HashMap<>();
        model.put("username", username);
        model.put("accountId", accountId);

        String subject;
        String templateName;

        if (lock) {
            subject = "Account Locked Notification";
            templateName = "lockUserEmail.ftl";
        } else {
            subject = "Account Unlocked Notification";
            templateName = "unlockUserEmail.ftl";
        }

        sendEmail(email, subject, templateName, model);
    }

    private void sendEmail(String to, String subject, String templateName, Map<String, Object> model) throws MessagingException, IOException, TemplateException {
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);

        helper.setFrom(from);
        helper.setTo(to);
        helper.setSubject(subject);

        Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
        cfg.setClassForTemplateLoading(this.getClass(), TEMPLATE_PATH);
        Template template = cfg.getTemplate(templateName);

        String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        helper.setText(html, true);

        javaMailSender.send(mimeMessage);
        log.info("Sent email to {}, subject={}", to, subject);
    }



}

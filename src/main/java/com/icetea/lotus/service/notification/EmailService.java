package com.icetea.lotus.service.notification;

import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;

import java.io.IOException;

public interface EmailService {
    void sendEmailMsg(String email, String msg, String subject) throws MessagingException, IOException, TemplateException;

    void sendEmailMsgAddNewUser(String username, String tempPassword, String email) throws MessagingException, IOException, TemplateException;

    void sendLockUserStatusEmail(String username, String accountId, String email, boolean lock) throws MessagingException, TemplateException, IOException;
}

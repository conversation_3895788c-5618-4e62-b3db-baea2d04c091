package com.icetea.lotus.service.exchange;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.PairEditRequest;
import com.icetea.lotus.dto.request.PairListRequest;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;

public interface PairManagementService {
    MessageResult getPairList(PageModel pageModel, PairListRequest pairListRequest);

    MessageResult getGeneralPairDetail(String symbol, HttpServletRequest httpServletRequest);

    MessageResult getPairDetailOfTypeTrading(String symbol, String typeTrading);

    MessageResult editPair(PairEditRequest pairEditRequest);

    void createNewPair(Coin baseCoin, Coin quoteCoin);

    MessageResult updateTradingStatus(String typeTrading, String symbol, Integer enable);

    MessageResult getRequiredPnlSummary(String symbol);
}

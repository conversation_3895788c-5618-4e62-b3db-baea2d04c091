package com.icetea.lotus.service.exchange.impl;

import com.icetea.lotus.client.ExternalApiClient;
import com.icetea.lotus.client.FutureApiClient;
import com.icetea.lotus.common.constants.Constants;
import com.icetea.lotus.common.exception.AdminInternalException;
import com.icetea.lotus.common.constants.SymbolStatus;
import com.icetea.lotus.common.exception.BadRequestException;
import com.icetea.lotus.common.exception.ServiceInterruptException;
import com.icetea.lotus.constant.AdminWalletType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dao.AdminWalletDao;
import com.icetea.lotus.dto.ContractSymbolDTO;
import com.icetea.lotus.dto.FutureServiceResponse;
import com.icetea.lotus.dto.MoneyDTO;
import com.icetea.lotus.dto.request.FutureSettingsRequest;
import com.icetea.lotus.dto.request.PairEditRequest;
import com.icetea.lotus.dto.request.PairGeneralInfoRequest;
import com.icetea.lotus.dto.request.PairListRequest;
import com.icetea.lotus.dto.request.SpotSettingsRequest;
import com.icetea.lotus.dto.response.PairDetailResponse;
import com.icetea.lotus.dto.response.PairListResponse;
import com.icetea.lotus.entity.spot.AdminWallet;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.pagination.Criteria;
import com.icetea.lotus.pagination.Restrictions;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.KeycloakService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.admin.AdminWalletManagementService;
import com.icetea.lotus.service.exchange.PairManagementService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class PairManagementServiceImpl implements PairManagementService {

    private final RestTemplate restTemplate;

    private final ExchangeCoinService exchangeCoinService;

    private final CoinService coinService;

    private final KeycloakService keycloakService;

    private final ExternalApiClient externalApiClient;

    private final LocaleMessageSourceService messageSource;
    private final FutureApiClient futureApiClient;
    private final AdminWalletManagementService adminWalletManagementService;
    private final AdminWalletDao adminWalletDao;

    @Value("${cex-services.future-api:future-api}")
    private String futureApiServiceName;

    private static final String PAIR_NOT_FOUND = "PAIR_NOT_FOUND";

    public static final String HTTP_PROTOCOL = "http://";

    /**
     * Retrieves a paginated list of trading pairs based on the provided page model and filter request.
     * Fetches contract symbols from the future service and builds the response with trading status.
     *
     * @param pageModel       the pagination and sorting information
     * @param pairListRequest the filter and search criteria for pairs
     * @return MessageResult containing the paginated pair list or error message
     */
    @Override
    public MessageResult getPairList(PageModel pageModel, PairListRequest pairListRequest) {
        try {
            // Build query criteria
            Criteria<ExchangeCoin> criteria = buildPairListCriteria(pairListRequest);
            PageModel newPageModel = buildPageModel(pageModel, pairListRequest);

            Page<ExchangeCoin> exchangeCoinPageResult = exchangeCoinService.findAll(criteria, newPageModel.getPageable());

            // Get all contract symbols from future service using the extracted method
            List<ContractSymbolDTO> contractSymbols = fetchContractSymbolsFromFutureService();

            // Build a list for quick check enable symbol in future
            List<String> contractSymbolFutureEnable = new ArrayList<>();
            for (ContractSymbolDTO contractSymbol : contractSymbols) {
                contractSymbolFutureEnable.add(contractSymbol.getSymbol());
            }
            // Map exchangeCoinPageResult to PairListResponse
            List<PairListResponse> pairListResponses = new ArrayList<>();
            List<ExchangeCoin> exchangeCoins = exchangeCoinPageResult.getContent();
            for (ExchangeCoin exchangeCoin : exchangeCoins) {

                Coin baseAsset = coinService.findByName(exchangeCoin.getCoinSymbol());
                String tokenImage = baseAsset != null ? baseAsset.getIconUrl() : null;
                // Get future trading status from enabled symbols list
                Integer futureTradingStatus = contractSymbolFutureEnable.contains(exchangeCoin.getSymbol()) ? 1 : 0;
                // Filter by future trading status
                if (pairListRequest.getFutureTradingStatus() != null
                        && !futureTradingStatus.equals(pairListRequest.getFutureTradingStatus()))
                    continue;
                PairListResponse pairListResponse = PairListResponse.builder()
                        .pairName(exchangeCoin.getSymbol())
                        .tokenImage(tokenImage)
                        .baseAsset(exchangeCoin.getCoinSymbol())
                        .quoteAsset(exchangeCoin.getBaseSymbol())
                        .spotTradingStatus(exchangeCoin.getEnable())
                        .futureTradingStatus(futureTradingStatus)
                        .build();
                pairListResponses.add(pairListResponse);
            }

            Map<String, Object> result = buildResultResponse(exchangeCoinPageResult, pairListResponses, pairListRequest, newPageModel);

            return MessageResult.success(result);
        } catch (ServiceInterruptException ex) {
            log.error("ServiceInterruptException fetching pair listing data", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("Error fetching pair listing data", ex);
            return MessageResult.error("Failed to fetch pair listing data");
        }
    }

    @Override
    public MessageResult getRequiredPnlSummary(String symbol) {
        return MessageResult.success(futureApiClient.getRequiredPnlAmount(symbol).getBody());
    }

    private BigDecimal getRequiredPnlAmount(String symbol) {
        return Objects.requireNonNull(futureApiClient.getRequiredPnlAmount(symbol).getBody()).getRequiredPnlAmount();
    }

    /**
     * Builds query criteria for ExchangeCoin based on the provided pair list request.
     *
     * @param pairListRequest the filter and search criteria for pairs
     * @return Criteria for querying ExchangeCoin
     */
    private @NotNull Criteria<ExchangeCoin> buildPairListCriteria(PairListRequest pairListRequest) {
        Criteria<ExchangeCoin> criteria = new Criteria<>();

        if (StringUtils.isNotBlank(pairListRequest.getBaseAsset())) {
            criteria.add(Restrictions.eq("coinSymbol", pairListRequest.getBaseAsset(), false));
        }
        if (StringUtils.isNotBlank(pairListRequest.getQuoteAsset())) {
            criteria.add(Restrictions.eq("baseSymbol", pairListRequest.getQuoteAsset(), false));
        }
        if (pairListRequest.getSpotTradingStatus() != null) {
            criteria.add(Restrictions.eq("enable", pairListRequest.getSpotTradingStatus(), false));
        }

        return criteria;
    }

    /**
     * Constructs a PageModel for pagination and sorting, optionally taking all results if future trading status is provided.
     *
     * @param pageModelRequest the original page model request
     * @param pairListRequest  the filter and search criteria for pairs
     * @return a new PageModel configured for the query
     */
    private @NotNull PageModel buildPageModel(PageModel pageModelRequest, PairListRequest pairListRequest) {
        // Set default sorting if not provided
        if (pageModelRequest.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("symbol");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.ASC);
            pageModelRequest.setProperty(list);
            pageModelRequest.setDirection(directions);
        }
        PageModel newPageModel = new PageModel();
        // Take all if futureTradingStatus is provided
        if (pairListRequest.getFutureTradingStatus() != null) {
            newPageModel.setPageNo(1);
            newPageModel.setPageSize(Integer.MAX_VALUE);
        } else {
            newPageModel.setPageNo(pageModelRequest.getPageNo());
            newPageModel.setPageSize(pageModelRequest.getPageSize());
        }
        newPageModel.setProperty(pageModelRequest.getProperty());
        newPageModel.setDirection(pageModelRequest.getDirection());
        return newPageModel;
    }

    /**
     * Fetches all enabled contract symbols from the future service API.
     *
     * @return List of ContractSymbolDTO representing enabled contract symbols
     * @throws ServiceInterruptException if the future service call fails
     */
    private @NotNull List<ContractSymbolDTO> fetchContractSymbolsFromFutureService() throws ServiceInterruptException {
        try {
            String futureUrl = HTTP_PROTOCOL + futureApiServiceName + "/future-api/api/v1/contracts/enabled";
            ResponseEntity<FutureServiceResponse<List<ContractSymbolDTO>>> result = restTemplate.exchange(
                    futureUrl,
                    org.springframework.http.HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<>() {
                    }
            );
            FutureServiceResponse<List<ContractSymbolDTO>> futureServiceResponse = result.getBody();
            if (futureServiceResponse == null || futureServiceResponse.getCode() != 200) {
                throw new ServiceInterruptException("Failed to get contract symbols from future service");
            }
            return futureServiceResponse.getData();
        } catch (Exception ex) {
            log.error("Failed to get contract symbols from future service", ex);
            throw new ServiceInterruptException("Failed to get contract symbols from future service", ex);
        }
    }

    /**
     * Builds the result response map containing paginated pair list and metadata.
     * Handles custom pagination when future trading status is filtered.
     *
     * @param exchangeCoinPageResult the page result of ExchangeCoin
     * @param pairListResponses      the filtered and mapped pair list responses
     * @param pairListRequest        the filter and search criteria for pairs
     * @param pageModel              the pagination and sorting information
     * @return Map containing the paginated result and metadata
     */
    private @NotNull Map<String, Object> buildResultResponse(Page<ExchangeCoin> exchangeCoinPageResult, List<PairListResponse> pairListResponses, PairListRequest pairListRequest, PageModel pageModel) {
        int pageNumber = exchangeCoinPageResult.getNumber();
        long pageSize = exchangeCoinPageResult.getSize();
        long totalElements = exchangeCoinPageResult.getTotalElements();
        long totalPages = exchangeCoinPageResult.getTotalPages();
        boolean last = exchangeCoinPageResult.isLast();

        List<PairListResponse> resultPairListings = pairListResponses;

        // Pagination when future trading status is provided
        if (pairListRequest.getFutureTradingStatus() != null) {
            pageNumber = pageModel.getPageNo() - 1;
            pageSize = pageModel.getPageSize();
            totalElements = pairListResponses.size();
            totalPages = (int) Math.ceil((double) totalElements / pageSize);
            last = pageNumber >= totalPages;

            resultPairListings = pairListResponses.stream()
                    .skip(pageNumber * pageSize)
                    .limit(pageModel.getPageSize())
                    .toList();
        }

        // Create response with pagination info
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("content", resultPairListings);
        result.put("pageNumber", pageNumber);
        result.put("pageSize", pageSize);
        result.put("totalElements", totalElements);
        result.put("totalPages", totalPages);
        result.put("last", last);
        return result;
    }

    /**
     * Retrieves general detailed information for a specific pair based on base and quote assets.
     *
     * @param symbol the base and quote assets of the pair
     * @return MessageResult containing the pair detail
     */
    @Override
    public MessageResult getGeneralPairDetail(String symbol, HttpServletRequest httpServletRequest) {
        try {
            ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(symbol);
            if (exchangeCoin == null) {
                return MessageResult.error(messageSource.getMessage(PAIR_NOT_FOUND));
            }
            Coin baseCoin = coinService.findByUnit(exchangeCoin.getCoinSymbol());
            if (baseCoin == null) {
                return MessageResult.error(messageSource.getMessage("BASE_COIN_NOT_FOUND"));
            }
            Coin quoteCoin = coinService.findByUnit(exchangeCoin.getBaseSymbol());
            if (quoteCoin == null) {
                return MessageResult.error(messageSource.getMessage("QUOTE_COIN_NOT_FOUND"));
            }
            if (httpServletRequest.getHeader("X-Timezone") == null) {
                return MessageResult.error(messageSource.getMessage("INVALID_TIMEZONE"));
            }
            ZoneId zoneId = ZoneId.of(httpServletRequest.getHeader("X-Timezone"));
            PairDetailResponse pairDetailResponse = PairDetailResponse.builder()
                    .baseAsset(exchangeCoin.getCoinSymbol())
                    .baseAssetName(baseCoin.getName())
                    .quoteAsset(exchangeCoin.getBaseSymbol())
                    .quoteAssetName(quoteCoin.getName())
                    .pairName(exchangeCoin.getDisplayName())
                    .createTime(OffsetDateTime.ofInstant(exchangeCoin.getCreatedAt(), zoneId))
                    .tickSize(exchangeCoin.getPriceStep())
                    .stepSize(exchangeCoin.getAmountStep())
                    .build();
            log.info("Pair detail of {} fetched successfully", symbol);
            return MessageResult.success(pairDetailResponse);
        } catch (Exception ex) {
            log.error("Error fetching pair detail", ex);
            return MessageResult.error("Failed to fetch pair detail");
        }
    }

    /**
     * Fetches detailed information for a specific pair based on base and quote assets.
     *
     * @param symbol      the base and quote assets of the pair
     * @param typeTrading the type of trading (spot or future)
     * @return MessageResult containing the pair detail
     */
    @Override
    public MessageResult getPairDetailOfTypeTrading(String symbol, String typeTrading) {
        try {
            if (typeTrading.equalsIgnoreCase("spot")) {
                ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(symbol);
                if (exchangeCoin == null) {
                    return MessageResult.error(messageSource.getMessage(PAIR_NOT_FOUND));
                }
                PairDetailResponse pairDetailResponse = PairDetailResponse.builder()
                        .spotTradingStatus(exchangeCoin.getEnable())
                        .spotTradingVisible(exchangeCoin.getVisible())
                        .minVolume(exchangeCoin.getMinVolume())
                        .maxVolume(exchangeCoin.getMaxVolume())
                        .minPrice(exchangeCoin.getMinPrice())
                        .maxPrice(exchangeCoin.getMaxPrice())
                        .minTotal(exchangeCoin.getMinTotal())
                        .maxTotal(exchangeCoin.getMaxTotal())
                        .takerFee(exchangeCoin.getTakerFeeRate().multiply(BigDecimal.TEN.pow(2)))
                        .makerFee(exchangeCoin.getMakerFeeRate().multiply(BigDecimal.TEN.pow(2)))
                        .build();
                log.info("Pair detail of {} of type trading fetched successfully", symbol);
                return MessageResult.success(pairDetailResponse);
            } else if (typeTrading.equalsIgnoreCase("future")) {
                ContractSymbolDTO contractSymbolDTO = fetchContractSymbolFromFutureService(symbol);
                PairDetailResponse pairDetailResponse = PairDetailResponse.builder()
                        .futureTradingStatus(contractSymbolDTO.getEnable())
                        .futureTradingVisible(Boolean.TRUE.equals(contractSymbolDTO.getVisible()) ? 1 : 0)
                        .minVolume(contractSymbolDTO.getMinVolume())
                        .maxVolume(contractSymbolDTO.getMaxVolume())
                        .minPrice(contractSymbolDTO.getMinPrice())
                        .maxPrice(contractSymbolDTO.getMaxPrice())
                        .minTotal(contractSymbolDTO.getMinTradeAmount().getValue())
                        .maxTotal(contractSymbolDTO.getMaxTradeAmount().getValue())
                        .takerFee(contractSymbolDTO.getTakerFeeRate().multiply(BigDecimal.TEN.pow(2)))
                        .makerFee(contractSymbolDTO.getMakerFeeRate().multiply(BigDecimal.TEN.pow(2)))
                        .baseLeverageStep(contractSymbolDTO.getBaseLeverageStep())
                        .liquidationFee(contractSymbolDTO.getLiquidationFeeRate().multiply(BigDecimal.TEN.pow(2)))
                        .maintenanceMarginRate(contractSymbolDTO.getMaintenanceMarginRate())
                        .build();
                log.info("Pair detail of {} of type trading fetched successfully", symbol);
                return MessageResult.success(pairDetailResponse);
            } else {
                throw new BadRequestException("Type trading is not supported");
            }
        } catch (BadRequestException ex) {
            log.error("Bad request while fetching pair detail of type trading", ex);
            throw ex;
        } catch (ServiceInterruptException ex) {
            log.error("Service interrupt while fetching pair detail of type trading", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("Error fetching pair detail of type trading", ex);
            return MessageResult.error("Failed to fetch pair detail of type trading");
        }
    }

    /**
     * Fetches enabled contract symbol by symbol from the future service API.
     *
     * @return ContractSymbolDTO representing enabled contract symbol
     * @throws ServiceInterruptException if the future service call fails
     */
    private @NotNull ContractSymbolDTO fetchContractSymbolFromFutureService(String symbol) throws ServiceInterruptException {
        try {
            String futureUrl = HTTP_PROTOCOL + futureApiServiceName + "/future-api/api/v1/contracts/find-by-symbol?symbol={symbol}";

            Map<String, Object> params = Map.of("symbol", symbol);

            ResponseEntity<FutureServiceResponse<ContractSymbolDTO>> result = restTemplate.exchange(
                    futureUrl,
                    org.springframework.http.HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<>() {
                    },
                    params
            );
            FutureServiceResponse<ContractSymbolDTO> futureServiceResponse = result.getBody();
            if (futureServiceResponse == null || futureServiceResponse.getCode() != 200) {
                log.error("Failed to get contract symbol from future service. Response: {}", futureServiceResponse);
                throw new ServiceInterruptException("Failed to get contract symbol from future service");
            }
            return futureServiceResponse.getData();
        } catch (Exception ex) {
            log.error("Failed to get contract symbol from future service", ex);
            throw new ServiceInterruptException("Failed to get contract symbol from future service", ex);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageResult editPair(PairEditRequest pairEditRequest) {

        PairGeneralInfoRequest pairGeneralInfoRequest = pairEditRequest.getPairGeneralInfo();
        FutureSettingsRequest futureSettingsRequest = pairEditRequest.getFutureSettingsRequest();
        SpotSettingsRequest spotSettingsRequest = pairEditRequest.getSpotSettingsRequest();

        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(pairEditRequest.getSymbol());
        if (exchangeCoin == null) {
            return MessageResult.error(messageSource.getMessage(PAIR_NOT_FOUND));
        }

        if(spotSettingsRequest != null) {
            // validate spot request
            MessageResult spotValidateResult = validateSpotSettingsRequest(spotSettingsRequest);
            if (spotValidateResult != null) {
                return spotValidateResult;
            }

            if (futureSettingsRequest != null) {
                // validate future request
                MessageResult futureValidateResult = validateFutureSettingsRequest(futureSettingsRequest, spotSettingsRequest);
                if (futureValidateResult != null) {
                    return futureValidateResult;
                }

                // edit future settings
                futureSettingsRequest.setStepSize(pairGeneralInfoRequest.getStepSize());
                futureSettingsRequest.setTickSize(pairGeneralInfoRequest.getTickSize());
                editFutureSettings(pairEditRequest.getFutureSettingsRequest(), pairEditRequest.getSymbol());
            }

            // edit spot settings
            editSpotSettings(pairEditRequest.getSpotSettingsRequest(), exchangeCoin);
        }

        // edit general info
        editGeneralInfo(pairGeneralInfoRequest, exchangeCoin);

        return MessageResult.success(messageSource.getMessage("EDIT_PAIR_SUCCESS"));
    }

    /**
     * Create new pair in spot and future with default values.
     *
     * @param baseCoin  the base coin
     * @param quoteCoin the quote coin
     * @throws BadRequestException     if the pair already exists
     * @throws ServiceInterruptException if the future service call fails
     * @throws AdminInternalException if the admin internal error occurs
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createNewPair(Coin baseCoin, Coin quoteCoin) throws BadRequestException, ServiceInterruptException, AdminInternalException {
        try {
            String symbol = createPairSymbol(baseCoin, quoteCoin);
            // 1. Check if pair already exists
            ExchangeCoin existPair = exchangeCoinService.findBySymbol(symbol);
            if (existPair != null) {
                throw new BadRequestException(messageSource.getMessage("PAIR_ALREADY_EXISTS"));
            }
            // 2. Create new pair in spot with default values
            createSpotPair(baseCoin, quoteCoin, symbol);

            // 3. Create new pair in future with default values (Call to future service)
            createFuturePair(baseCoin, quoteCoin, symbol);

            log.info("New pair {} created successfully", symbol);
        } catch (BadRequestException ex) {
            log.error("Bad request while creating new pair", ex);
            throw ex;
        } catch (ServiceInterruptException ex) {
            log.error("Service interrupt while creating new pair", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("Error creating new pair", ex);
            throw new AdminInternalException("Failed to create new pair");
        }
    }

    /**
     * Create pair symbol.
     *
     * @param baseCoin  the base coin
     * @param quoteCoin the quote coin
     * @return the pair symbol
     */
    private String createPairSymbol(Coin baseCoin, Coin quoteCoin) {
        return "%s/%s".formatted(baseCoin.getUnit(), quoteCoin.getUnit());
    }

    /**
     * Create new pair in spot with default values.
     *
     * @param baseCoin  the base coin
     * @param quoteCoin the quote coin
     * @param symbol    the pair symbol
     */
    private void createSpotPair(Coin baseCoin, Coin quoteCoin, String symbol) {
        ExchangeCoin newExchangeCoin = new ExchangeCoin();

        // General info with default values
        newExchangeCoin.setSymbol(symbol);
        newExchangeCoin.setDisplayName(symbol.replace("/", ""));
        newExchangeCoin.setCoinSymbol(baseCoin.getUnit());
        newExchangeCoin.setBaseSymbol(quoteCoin.getUnit());

        // Not null fields
        newExchangeCoin.setCoinScale(baseCoin.getCoinScale());
        newExchangeCoin.setBaseCoinScale(quoteCoin.getCoinScale());
        newExchangeCoin.setSort(0);

        // Spot trading settings with default values
        newExchangeCoin.setEnable(0); // Disabled by default
        newExchangeCoin.setVisible(0); // Hidden by default

        // Save the new pair to spot database
        exchangeCoinService.save(newExchangeCoin);
    }

    /**
     * Create new pair in future with default values.
     *
     * @param baseCoin  the base coin
     * @param quoteCoin the quote coin
     * @param symbol    the pair symbol
     */
    private void createFuturePair(Coin baseCoin, Coin quoteCoin, String symbol) {
        ContractSymbolDTO contractSymbolDTO = ContractSymbolDTO.builder()
                .baseSymbol(baseCoin.getUnit())
                .quoteSymbol(quoteCoin.getUnit())
                .symbol(symbol)
                .enable(0)
                .visible(false)
                .build();
        createFuturePair(contractSymbolDTO);
    }

    /**
     * Create new contract symbol in future service.
     *
     * @param contractSymbolDTO the contract symbol DTO
     * @throws ServiceInterruptException if the future service call fails
     */
    private void createFuturePair(ContractSymbolDTO contractSymbolDTO) throws ServiceInterruptException {
        try {
            String futureUrl = HTTP_PROTOCOL + futureApiServiceName + "/future-api/api/v1/contracts";

            Jwt currentToken = keycloakService.getCurrentJwt();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + currentToken.getTokenValue());

            HttpEntity<ContractSymbolDTO> entity = new HttpEntity<>(contractSymbolDTO, headers);

            ResponseEntity<ContractSymbolDTO> result = restTemplate.exchange(
                    futureUrl,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<>() {
                    }
            );
            if (!result.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to post new contract symbol to future service. Response: {}", result);
                throw new ServiceInterruptException("Failed to post new contract symbol to future service");
            }
            log.info("New contract symbol {} created successfully", contractSymbolDTO.getSymbol());
        } catch (ServiceInterruptException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("Failed to post new contract symbol to future service", ex);
            throw new ServiceInterruptException("Failed to post new contract symbol to future service", ex);
        }
    }

    private MessageResult validateFutureSettingsRequest(FutureSettingsRequest futureRequest, SpotSettingsRequest spotRequest) {
        // 0. Spot Settings must be enabled before enable Future Settings
        if(Constants.DISABLE_STATUS.equals(spotRequest.getStatus()) && Constants.ENABLE_STATUS.equals(futureRequest.getStatus())){
            return MessageResult.error(messageSource.getMessage("FUTURE_REQUIRED"));
        }

        // 1. Min Order Quantity > Max Order Quantity
        if (futureRequest.getMinVolume() != null && futureRequest.getMaxVolume() != null &&
                futureRequest.getMinVolume().compareTo(futureRequest.getMaxVolume()) > 0) {
            return MessageResult.error(messageSource.getMessage("FUTURE_MIN_VOLUME_INVALID"));
        }

        // 2. Max Order Price < Min Order Price
        if (futureRequest.getMinPrice() != null && futureRequest.getMaxPrice() != null &&
                futureRequest.getMaxPrice().compareTo(futureRequest.getMinPrice()) < 0) {
            return MessageResult.error(messageSource.getMessage("FUTURE_MAX_PRICE_INVALID"));
        }

        // 3. Min Order Notional > Max Order Notional
        if (futureRequest.getMinTotal() != null && futureRequest.getMaxTotal() != null &&
                futureRequest.getMinTotal().compareTo(futureRequest.getMaxTotal()) > 0) {
            return MessageResult.error(messageSource.getMessage("FUTURE_MIN_NOTIONAL_INVALID"));
        }
        return null;
    }

    private MessageResult validateSpotSettingsRequest(SpotSettingsRequest request) {
        // 1. Min Order Quantity > Max Order Quantity
        if (request.getMinVolume() != null && request.getMaxVolume() != null &&
                request.getMinVolume().compareTo(request.getMaxVolume()) > 0) {
            return MessageResult.error(messageSource.getMessage("SPOT_MIN_VOLUME_INVALID"));
        }

        // 2. Max Order Price < Min Order Price
        if (request.getMinPrice() != null && request.getMaxPrice() != null &&
                request.getMaxPrice().compareTo(request.getMinPrice()) < 0) {
            return MessageResult.error(messageSource.getMessage("SPOT_MAX_PRICE_INVALID"));
        }

        // 3. Min Order Notional > Max Order Notional
        if (request.getMinTotal() != null && request.getMaxTotal() != null &&
                request.getMinTotal().compareTo(request.getMaxTotal()) > 0) {
            return MessageResult.error(messageSource.getMessage("SPOT_MIN_NOTIONAL_INVALID"));
        }
        return null;
    }

    private void editGeneralInfo(PairGeneralInfoRequest request, ExchangeCoin exchangeCoin) {
        if (exchangeCoinService.countByDisplayNameAndSymbolNot(request.getPairName(), exchangeCoin.getSymbol()) > 0) {
            throw new BadRequestException(messageSource.getMessage("DISPLAY_NAME_EXISTS"));
        }

        exchangeCoin.setDisplayName(request.getPairName());
        exchangeCoin.setPriceStep(request.getTickSize());
        exchangeCoin.setAmountStep(request.getStepSize());

        exchangeCoinService.save(exchangeCoin);
    }


    private void editSpotSettings(SpotSettingsRequest request, ExchangeCoin exchangeCoin) {
        if (request.getStatus() != null && Constants.ENABLE_STATUS.equals(request.getStatus())) {
            exchangeCoin.setEnable(Constants.ENABLE_STATUS);
        }

        exchangeCoin.setVisible(request.getVisibleStatus());
        exchangeCoin.setMinVolume(request.getMinVolume());
        exchangeCoin.setMaxVolume(request.getMaxVolume());
        exchangeCoin.setMinPrice(request.getMinPrice());
        exchangeCoin.setMaxPrice(request.getMaxPrice());
        exchangeCoin.setMinTotal(request.getMinTotal());
        exchangeCoin.setMaxTotal(request.getMaxTotal());
        exchangeCoin.setTakerFeeRate(toRate(request.getTakerFee()));
        exchangeCoin.setMakerFeeRate(toRate(request.getMakerFee()));
        exchangeCoinService.save(exchangeCoin);
    }

    private void editFutureSettings(FutureSettingsRequest request, String symbol) {
        ContractSymbolDTO contractSymbolDTO = fetchContractSymbolFromFutureService(symbol);

        if (request.getStatus() != null && Constants.ENABLE_STATUS.equals(request.getStatus())) {
            contractSymbolDTO.setEnable(Constants.ENABLE_STATUS);
        }


        contractSymbolDTO.setVisible(request.getVisibleStatus() == 1);

        contractSymbolDTO.setMinVolume(request.getMinVolume());
        contractSymbolDTO.setMaxVolume(request.getMaxVolume());

        contractSymbolDTO.setMinPrice(request.getMinPrice());
        contractSymbolDTO.setMaxPrice(request.getMaxPrice());

        contractSymbolDTO.setMinTradeAmount(MoneyDTO.of(request.getMinTotal()));
        contractSymbolDTO.setMaxTradeAmount(MoneyDTO.of(request.getMaxTotal()));

        contractSymbolDTO.setTakerFeeRate(toRate(request.getTakerFee()));
        contractSymbolDTO.setMakerFeeRate(toRate(request.getMakerFee()));

        BigDecimal leverageMax = BigDecimal.valueOf(request.getBaseLeverageStep() * Constants.MAX_LEVERAGE_MULTIPLIER);
        contractSymbolDTO.setBaseLeverageStep(request.getBaseLeverageStep());
        contractSymbolDTO.setDefaultLeverage(leverageMax);
        contractSymbolDTO.setLeverageMin(Constants.LEVERAGE_MIN);
        contractSymbolDTO.setLeverageMax(leverageMax);

        contractSymbolDTO.setLiquidationFeeRate(toRate(request.getLiquidationFee()));

        contractSymbolDTO.setMaintenanceMarginRate(toRate(request.getMaintenanceMargin()));

        contractSymbolDTO.setAmountStep(request.getStepSize());
        contractSymbolDTO.setPriceStep(request.getTickSize());

        editSymbolContract(contractSymbolDTO.getId(), contractSymbolDTO);
    }

    private void editSymbolContract(Long id, ContractSymbolDTO contractSymbolDTO) {
        try {
            String futureUrl = HTTP_PROTOCOL + futureApiServiceName + "/future-api/api/v1/contracts/{id}";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            HttpEntity<ContractSymbolDTO> requestEntity = new HttpEntity<>(contractSymbolDTO, headers);

            ResponseEntity<FutureServiceResponse<ContractSymbolDTO>> result = restTemplate.exchange(
                    futureUrl,
                    HttpMethod.PUT,
                    requestEntity,
                    new ParameterizedTypeReference<>() {
                    },
                    id
            );

            FutureServiceResponse<ContractSymbolDTO> futureServiceResponse = result.getBody();

            if (futureServiceResponse == null || futureServiceResponse.getCode() != 0) {
                log.error("Failed to edit contract symbol. Response: {}", futureServiceResponse);
                throw new ServiceInterruptException("Failed to edit contract symbol");
            }

        } catch (Exception ex) {
            log.error("Failed to edit contract symbol", ex);
            throw new ServiceInterruptException("Failed to edit contract symbol", ex);
        }
    }

    private BigDecimal toRate(BigDecimal percent) {
        if (percent == null) {
            return BigDecimal.ZERO;
        }
        return percent.divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageResult updateTradingStatus(String typeTrading, String symbol, Integer enable) {
        if (Constants.TRADING_TYPE_SPOT.equalsIgnoreCase(typeTrading)) {
            return handleSpotTradingStatus(symbol, enable);
        }

        if (Constants.TRADING_TYPE_FUTURE.equalsIgnoreCase(typeTrading)) {
            return handleFutureTradingStatus(symbol, enable);
        }

        throw new BadRequestException(messageSource.getMessage("TRADING_TYPE_NOT_SUPPORTED", new Object[]{typeTrading}));
    }

    private MessageResult handleSpotTradingStatus(String symbol, Integer enable) {
        // Disable future first when spot is disabled
        if (Constants.DISABLE_STATUS.equals(enable)) {
            MessageResult result = disableFutureSymbol(symbol);
            if (!Constants.MESSAGE_RESULT_SUCCESS_CODE.equals(result.getCode())) {
                return result;
            }
        }
        return updateSpotTradingStatus(symbol, enable);
    }

    private MessageResult handleFutureTradingStatus(String symbol, Integer enable) {
        // Enable spot first when future is enabled
        if (Constants.ENABLE_STATUS.equals(enable)) {
            ensureSpotEnabled(symbol);
            return updateFutureTradingStatus(symbol, SymbolStatus.ENABLE);
        }
         return disableFutureSymbol(symbol);
    }

    private void ensureSpotEnabled(String symbol) {
        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(symbol);
        if (exchangeCoin == null) {
            throw new BadRequestException(messageSource.getMessage(PAIR_NOT_FOUND));
        }
        if (Constants.DISABLE_STATUS.equals(exchangeCoin.getEnable())) {
            throw new BadRequestException(messageSource.getMessage("SPOT_REQUIRED"));
        }
    }

    /**
     * Disable future trading with PnL sufficiency check.
     * If admin has sufficient PnL balance, disable directly.
     * Else set to pending disable status.
     * @param symbol the trading pair symbol
     * @return MessageResult indicating success or failure of the operation
     */
    private MessageResult disableFutureSymbol(String symbol) {
        // Calculate required PnL amount from the user perspective
        BigDecimal requiredPnlAmount = getRequiredPnlAmount(symbol);
        log.info("Calculated required PnL amount for symbol {}: {}", symbol, requiredPnlAmount);

        boolean hasSufficientBalance;
        // If requiredPnlAmount ≤ 0 => admin already has profit and can disable directly
        if (requiredPnlAmount.compareTo(BigDecimal.ZERO) <= 0) {
            hasSufficientBalance = true;
            log.info("Symbol {} can be disabled directly since profit amount is {}", symbol, requiredPnlAmount);
        } else {
            AdminWallet adminWallet = adminWalletDao.findByWalletTypeAndStatus(AdminWalletType.PNL, Constants.ENABLE_STATUS);
            // Else requiredPnlAmount > 0 => need to check admin wallet balance sufficiency
            hasSufficientBalance = adminWalletManagementService.isSufficientPnlBalance(requiredPnlAmount, adminWallet);
            log.info("Admin wallet sufficient balance check for symbol {}: {}", symbol, hasSufficientBalance);
        }

        // Determine final status
        SymbolStatus targetStatus = hasSufficientBalance
                ? SymbolStatus.DISABLE
                : SymbolStatus.PENDING_DISABLE;

        log.info("Future trading status for symbol {} will be updated to {}", symbol, targetStatus);

        MessageResult result = updateFutureTradingStatus(symbol, targetStatus);
        if (Constants.MESSAGE_RESULT_SUCCESS_CODE == result.getCode() && hasSufficientBalance) {
            adminWalletManagementService.calculateTotalPnlBalance(requiredPnlAmount);
            log.info("Future trading for symbol {} disabled successfully", symbol);
        }
        // Update future trading status
        return result;
    }

    private MessageResult updateSpotTradingStatus(String symbol, Integer enable) {
        try {
            ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(symbol);
            if (exchangeCoin == null) {
                throw new BadRequestException(messageSource.getMessage(PAIR_NOT_FOUND));
            }
            exchangeCoin.setEnable(enable);
            exchangeCoinService.save(exchangeCoin);
            if (Constants.DISABLE_STATUS.equals(enable)) {
                externalApiClient.cancelAllSpotOrders(symbol);
            }
            return MessageResult.success();
        } catch (BadRequestException ex) {
            log.error("Bad request while updating spot trading status", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("Error while updating spot trading status", ex);
            throw ex;
        }
    }

    private MessageResult updateFutureTradingStatus(String symbol, SymbolStatus status) {
        try {
            ResponseEntity<String> response = futureApiClient.updateFutureContractSymbol(symbol, status);
            return MessageResult.success(response.getBody());
        } catch (BadRequestException ex) {
            log.error("Bad request while updating future trading status", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("Error while updating future trading status", ex);
            throw ex;
        }
    }
}

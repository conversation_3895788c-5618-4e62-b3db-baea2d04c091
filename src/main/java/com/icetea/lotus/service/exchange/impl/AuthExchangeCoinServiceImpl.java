package com.icetea.lotus.service.exchange.impl;

import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.exchange.AuthExchangeCoinService;
import com.icetea.lotus.util.MessageResult;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static org.springframework.util.Assert.notNull;

/**
 * The type Auth exchange coin service.
 */
@Service
@RequiredArgsConstructor
public class AuthExchangeCoinServiceImpl extends BaseController implements AuthExchangeCoinService {

    private final ExchangeCoinService exchangeCoinService;

    @Override
    public MessageResult detail(String symbol, String sign) {
        // validate sign
        if (!sign.equals("77585211314qazwsx")) {
            return error("Illegal access");
        }
        // find data in ExchangeCoin by symbol
        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(symbol);
        notNull(exchangeCoin, "validate symbol!");
        return success(exchangeCoin);
    }

    @Override
    public MessageResult modifyLimit(String symbol, BigDecimal maxBuyPrice, BigDecimal minSellPrice, String sign) {
        // validate sign
        if (!sign.equals("77585211314qazwsx")) {
            return error("Illegal access");
        }
        //find ExchangeCoin by sympol
        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(symbol);
        notNull(exchangeCoin, "validate symbol!");

        //update min/max price
        exchangeCoin.setMaxBuyPrice(maxBuyPrice);
        exchangeCoin.setMinSellPrice(minSellPrice);

        // save db
        exchangeCoinService.save(exchangeCoin);
        return success(exchangeCoin);
    }
}

package com.icetea.lotus.service.system.impl;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.core.Menu;
import com.icetea.lotus.dao.SysPermissionDao;
import com.icetea.lotus.dto.RoleDto;
import com.icetea.lotus.entity.spot.SysPermission;
import com.icetea.lotus.entity.spot.SysRole;
import com.icetea.lotus.service.SysRoleService;
import com.icetea.lotus.service.system.ExtendedSysRoleService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * The type Extended sys role service.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExtendedSysRoleServiceImpl extends BaseController implements ExtendedSysRoleService {

    private final SysRoleService sysRoleService;
    private final SysPermissionDao sysPermissionRepository;


    /**
     * Modify roles
     *
     * @param roleDto the sys role
     * @return message result
     */
    @Override
    public MessageResult mergeRole(RoleDto roleDto) {
        SysRole sysRole = sysRoleService.findOne(roleDto.getId());
        if (sysRole == null) {
            return error(500, "Role does not exist");
        }
        List<SysPermission> permissions = sysPermissionRepository.findAllById(roleDto.getPermissionIds());
        sysRole.setPermissions(permissions);

        sysRole = sysRoleService.save(sysRole);

        return success("Operation is successful", sysRole);

    }

    @Override
    public MessageResult allMenu() {
        List<Menu> list = sysRoleService.toMenus(sysRoleService.getAllPermission(), 0L);
        MessageResult result = success("success");
        result.setData(list);
        return result;
    }

    @Override
    public MessageResult roleAllPermission(Long roleId) {
        List<Menu> content = sysRoleService.toMenus(sysRoleService.findOne(roleId).getPermissions(), 0L);
        MessageResult result = success();
        result.setData(content);
        return result;
    }

    @Override
    public MessageResult getAllRole(PageModel pageModel) {
        Page<SysRole> all = sysRoleService.findAll(null, pageModel);
        return success(all);
    }

    @Override
    public MessageResult deletes(Long id) {
        return sysRoleService.deletes(id);
    }
}

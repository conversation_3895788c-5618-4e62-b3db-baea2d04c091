package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.create.DataDictionaryCreate;
import com.icetea.lotus.model.update.DataDictionaryUpdate;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedDataDictionaryService {
    MessageResult post(DataDictionaryCreate model);

    MessageResult page(PageModel pageModel);

    MessageResult put(String bond, DataDictionaryUpdate model);
}

package com.icetea.lotus.service.system.impl;

import com.icetea.lotus.constant.AdminStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.dao.AdminDao;
import com.icetea.lotus.dto.response.AdminResponse;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.system.AdminService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminServiceImpl extends BaseController implements AdminService {
    private final AdminDao adminDao;

    @Override
    public MessageResult getAdmins(String keyword, Integer roleId, AdminStatus status, PageModel pageModel) {
        int pageIndex = pageModel.getPageNo() > 0 ? pageModel.getPageNo() - 1 : 0;
        Pageable pageable = PageRequest.of(pageIndex, pageModel.getPageSize());
        Page<Admin> adminPage = adminDao.getAdmins(keyword, roleId, status, pageable);
        List<AdminResponse> adminResponseList = adminPage.getContent().stream()
                .map(admin -> AdminResponse.builder()
                        .id(admin.getId())
                        .email(admin.getEmail())
                        .phone(admin.getMobilePhone())
                        .roleId(admin.getRoleId())
                        .realName(admin.getRealName())
                        .status(admin.getStatus().getOrdinal())

                        .build())
                .toList();
        return success(new PageImpl<>(adminResponseList, pageable, adminPage.getTotalElements()));
    }
}

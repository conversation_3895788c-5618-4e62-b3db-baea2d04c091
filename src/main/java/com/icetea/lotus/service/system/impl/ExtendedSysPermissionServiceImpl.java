package com.icetea.lotus.service.system.impl;

import org.springframework.stereotype.Service;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.QSysPermission;
import com.icetea.lotus.entity.spot.SysPermission;
import com.icetea.lotus.service.SysPermissionService;
import com.icetea.lotus.service.system.ExtendedSysPermissionService;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;


/**
 * The type Extended sys permission service.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExtendedSysPermissionServiceImpl extends BaseController implements ExtendedSysPermissionService {

    private final SysPermissionService sysPermissionService;

    @Override
    public MessageResult merge(SysPermission permission) {
        permission = sysPermissionService.save(permission);
        MessageResult result = success("Save permission successfully");
        result.setData(permission);
        return result;
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel, Long parentId) {
        BooleanExpression predicate = null;
        if (parentId != null) {
            predicate = QSysPermission.sysPermission.parentId.eq(parentId);
        }
        Page<SysPermission> all = sysPermissionService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult detail(Long id) {
        SysPermission sysPermission = sysPermissionService.findOne(id);
        if (sysPermission == null) {
            return error("The permission does not exist");
        }
        return MessageResult.getSuccessInstance("Query permissions were successful", sysPermission);
    }

    @Override
    public MessageResult deletes(Long[] ids) {
        sysPermissionService.deletes(ids);
        return MessageResult.success("Batch deletion permission succeeded");
    }
}

package com.icetea.lotus.service.system.impl;

import com.icetea.lotus.common.constants.Constants;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.common.constants.MethodTokenConstants;
import com.icetea.lotus.common.exception.AdminInternalException;
import com.icetea.lotus.common.exception.BadRequestException;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.common.exception.ServiceInterruptException;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.ContractSymbolDTO;
import com.icetea.lotus.dto.FutureServiceResponse;
import com.icetea.lotus.dto.request.EditTokenRequest;
import com.icetea.lotus.dto.request.SaveTokenRequest;
import com.icetea.lotus.dto.request.TokenContractRequest;
import com.icetea.lotus.dto.request.TokenListRequest;
import com.icetea.lotus.dto.response.TokenContractResponse;
import com.icetea.lotus.dto.response.TokenDetailResponse;
import com.icetea.lotus.dto.response.TokenListingResponse;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.Coinext;
import com.icetea.lotus.entity.spot.Coinprotocol;
import com.icetea.lotus.entity.spot.HotTransferRecord;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.QHotTransferRecord;
import com.icetea.lotus.pagination.Criteria;
import com.icetea.lotus.pagination.Restrictions;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinextService;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.HotTransferRecordService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.exchange.PairManagementService;
import com.icetea.lotus.service.system.ExtendedCoinService;
import com.icetea.lotus.service.system.Web3Service;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.JDBCUtils;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.datatypes.generated.Uint8;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.util.Assert.notNull;

/**
 * The type Extended coin service.
 */
@Slf4j
@Service
public class ExtendedCoinServiceImpl extends BaseAdminController implements ExtendedCoinService {

    private final HotTransferRecordService hotTransferRecordService;

    private final CoinService coinService;

    private final CoinextService coinextService;

    private final CoinprotocolService coinprotocolService;

    private final PairManagementService pairManagementService;

    private final MemberWalletService memberWalletService;

    private final RestTemplate restTemplate;

    @SuppressWarnings("rawtypes")
    private final RedisTemplate redisTemplate;

    private final MemberWalletService walletService;

    private final MemberService memberService;

    private final LocaleMessageSourceService messageSource;

    private final MemberTransactionService memberTransactionService;

    private final JDBCUtils jdbcUtils;

    private final Web3Service web3Service;

    @Value("${cex-services.market:market}")
    private String marketServiceName;

    @Value("${cex-services.future-api:future-api}")
    private String futureApiServiceName;

    private static final String MESSAGE_COIN_NAME_NOT_EXIST = "COIN_NAME_NOT_EXIST";
    private static final String SERVICE_RPC_URL = "serviceRpcUrl";
    private static final String MESSAGE_FAILED_SAVE_TOKEN = "FAILED_TO_SAVE_TOKEN_INFO";

    public ExtendedCoinServiceImpl(BaseAdminService baseAdminService, HotTransferRecordService hotTransferRecordService, CoinService coinService, CoinextService coinextService, CoinprotocolService coinprotocolService, PairManagementService pairManagementService, MemberWalletService memberWalletService, RestTemplate restTemplate, @SuppressWarnings("rawtypes") RedisTemplate redisTemplate, MemberWalletService walletService, MemberService memberService, LocaleMessageSourceService messageSource, MemberTransactionService memberTransactionService, JDBCUtils jdbcUtils, Web3Service web3Service) {
        super(baseAdminService);
        this.hotTransferRecordService = hotTransferRecordService;
        this.coinService = coinService;
        this.coinextService = coinextService;
        this.coinprotocolService = coinprotocolService;
        this.pairManagementService = pairManagementService;
        this.memberWalletService = memberWalletService;
        this.restTemplate = restTemplate;
        this.redisTemplate = redisTemplate;
        this.walletService = walletService;
        this.memberService = memberService;
        this.messageSource = messageSource;
        this.memberTransactionService = memberTransactionService;
        this.jdbcUtils = jdbcUtils;
        this.web3Service = web3Service;
    }

    @Override
    public MessageResult create(Coin coin) {
        Coin one = coinService.findOne(coin.getName());
        if (one != null) {
            return error(messageSource.getMessage("COIN_NAME_EXIST"));
        }
        coin.setWithdrawThreshold(BigDecimal.ZERO);
        coin.setCanAutoWithdraw(BooleanEnum.IS_FALSE);
        coin.setMinWithdrawAmount(BigDecimal.ZERO);
        coin.setMaxWithdrawAmount(BigDecimal.ZERO);
        coin.setMinTxFee(0.00);
        coin.setMaxTxFee(0.00);
        coin.setMinRechargeAmount(BigDecimal.ZERO);
        coin.setCanWithdraw(BooleanEnum.IS_FALSE);
        coin.setEnableRpc(BooleanEnum.IS_FALSE);
        coin.setCanRecharge(BooleanEnum.IS_FALSE);
        coin.setDepositAddress("0");
        coin.setAccountType(0);
        coinService.save(coin);

        jdbcUtils.synchronization2MemberRegisterWallet(null, coin.getName());

        return success();
    }

    @Override
    public MessageResult getAllCoinName() {
        List<Coin> list = coinService.findAll();
        List<Map<String, Object>> result = new ArrayList<>();
        for (Coin coin : list) {
            Map<String, Object> subData = new HashMap<>();
            subData.put("coinName", coin.getName());
            subData.put("coinImage", coin.getIconUrl());
            result.add(subData);
        }
        return success(result);
    }

    @Override
    public MessageResult getAllCoinNameAndUnit() {
        List<CoinDTO> list = coinService.getAllCoinNameAndUnit();
        return success(list);
    }

    @Override
    public MessageResult getAllCoinNameLegal() {
        List<String> list = coinService.getAllCoinNameLegal();
        return success(list);
    }

    @Override
    public MessageResult update(Coin coin) {
        Coin one = coinService.findOne(coin.getName());
        notNull(one, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        coinService.save(coin);
        return success();
    }

    @Override
    public MessageResult detail(String name) {
        Coin coin = coinService.findOne(name);
        notNull(coin, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        return success(coin);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel) {
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("name");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<Coin> pageResult = coinService.findAll((Predicate) null, pageModel.getPageable());
        for (Coin coin : pageResult.getContent()) {
            if (coin.getEnableRpc().getOrdinal() == 1) {
                coin.setAllBalance(memberWalletService.getAllBalance(coin.getName()));
                log.info(coin.getAllBalance() + "==============");
                if (coin.getAccountType() == 1) {
                    coin.setHotAllBalance(memberWalletService.getAllBalance(coin.getName()));
                    coin.setBlockHeight(0L);
                } else {
                    String url = SERVICE_RPC_URL + coin.getUnit() + "/rpc/balance";
                    coin.setHotAllBalance(getRPCWalletBalance(url, coin.getUnit()));

                    String url2 = SERVICE_RPC_URL + coin.getUnit() + "/rpc/height";
                    coin.setBlockHeight(getRPCBlockHeight(url2));
                }
            }
        }
        return success(pageResult);
    }

    private BigDecimal getRPCWalletBalance(String url, String unit) {
        try {
            // String url = "http://" + serviceName + "/rpc/address/{account}"
            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class);
            log.info("getRPCWalletBalance: result={}", result);
            if (result.getStatusCode().value() == 200) {
                MessageResult mr = result.getBody();
                if (mr.getCode() == 0) {
                    String balance = mr.getData().toString();
                    BigDecimal bigDecimal = new BigDecimal(balance);
                    log.info(unit + messageSource.getMessage("HOT_WALLET_BALANCE"), bigDecimal);
                    return bigDecimal;
                }
            }
        } catch (IllegalStateException e) {
            log.error("getRPCWalletBalance IllegalStateException={}", e);
            return new BigDecimal("0");
        } catch (Exception e) {
            log.error("getRPCWalletBalance Exception={}", e);
            return new BigDecimal("0");
        }
        return new BigDecimal("0");
    }

    private Long getRPCBlockHeight(String url) {
        try {
            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class);
            log.info("getRPCBlockHeight: result={}", result);
            if (result.getStatusCode().value() == 200) {
                MessageResult mr = result.getBody();
                if (mr.getCode() == 0) {
                    String height = mr.getData().toString();
                    return Long.valueOf(height);
                }
            }

        } catch (Exception e) {
            log.error("getRPCBlockHeight Exception={}", e.toString());
            return 0L;
        }
        return 0L;
    }


    @Override
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Coin> all = coinService.findAll();
        return new FileUtil<Coin>(messageSource).exportExcel(request, response, all, "coin");
    }

    @Override
    public MessageResult delete(String name) {
        Coin coin = coinService.findOne(name);
        notNull(coin, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        coinService.deleteOne(name);
        return success();
    }

    @Override
    public MessageResult setPlatformCoin(String name) {
        Coin coin = coinService.findOne(name);
        notNull(coin, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        coinService.setPlatformCoin(coin);
        return success();
    }

    @Override
    public MessageResult transfer(Admin admin, BigDecimal amount, String unit, String code) {
        Assert.notNull(admin, "The session has expired, please log in again");

        String key = SysConstant.ADMIN_COIN_TRANSFER_COLD_PREFIX + admin.getMobilePhone();

        @SuppressWarnings("rawtypes")
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object object = valueOperations.get(key + "_PASS");

        if (object == null) {
            MessageResult checkCode = checkCode(code, key);
            if (checkCode.getCode() != 0) {
                return checkCode;
            }
        }
        Coin coin = coinService.findByUnit(unit);
        String urlBalance = SERVICE_RPC_URL + coin.getUnit() + "/rpc/balance";
        BigDecimal balance = getRPCWalletBalance(urlBalance, coin.getUnit());
        log.info("balance:-------{}", balance);
        if (amount.compareTo(balance) > 0) {
            return error(messageSource.getMessage("HOT_WALLET_BALANCE_POOL"));
        }
        String url = SERVICE_RPC_URL + coin.getUnit() + "/rpc/transfer?address={1}&amount={2}&fee={3}";
        MessageResult result = restTemplate.getForObject(url,
                MessageResult.class, coin.getColdWalletAddress(), amount, coin.getMinerFee());
        log.info("result = {}", result);
        if (result.getCode() == 0 && result.getData() != null) {
            HotTransferRecord hotTransferRecord = new HotTransferRecord();
            hotTransferRecord.setAdminId(admin.getId());
            hotTransferRecord.setAdminName(admin.getUsername());
            hotTransferRecord.setAmount(amount);
            hotTransferRecord.setBalance(balance.subtract(amount));
            hotTransferRecord.setMinerFee(coin.getMinerFee() == null ? BigDecimal.ZERO : coin.getMinerFee());
            hotTransferRecord.setUnit(unit.toUpperCase());
            hotTransferRecord.setColdAddress(coin.getColdWalletAddress());
            hotTransferRecord.setTransactionNumber(result.getData().toString());
            hotTransferRecordService.save(hotTransferRecord);
            return success(hotTransferRecord);
        }
        return error(messageSource.getMessage("REQUEST_FAILED"));
    }

    @Override
    public MessageResult page(PageModel pageModel, String unit) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (StringUtils.isNotBlank(unit)) {
            booleanExpressions.add(QHotTransferRecord.hotTransferRecord.unit.eq(unit));
        }
        Page<HotTransferRecord> page = hotTransferRecordService.findAll(PredicateUtils.getPredicate(booleanExpressions), pageModel);
        return success(page);
    }

    @Override
    public MessageResult createCoin(String coinName) {
        Coin coin = coinService.findOne(coinName);
        if (coin == null) {
            return MessageResult.error("Currency configuration does not exist");
        }
        jdbcUtils.synchronization2MemberRegisterWallet(null, coin.getName());
        // To re-enable wallet creation for all members, uncomment the following line:
        // createWalletsForAllMembers(coin)
        return success();
    }

    /**
     * Creates wallets for all members for the specified coin if they do not already exist.
     *
     * @param coin the coin for which wallets should be created for all members
     */
    @SuppressWarnings("unused")
    private void createWalletsForAllMembers(Coin coin) {
        List<Member> members = memberService.findAll();
        for (Member member : members) {
            createWalletForMemberIfAbsent(coin, member);
        }
    }

    /**
     * Creates a wallet for the given member and coin if it does not already exist.
     */
    private void createWalletForMemberIfAbsent(Coin coin, Member member) {
        MemberWallet wallet = memberWalletService.findByCoinAndMember(coin, member);
        if (wallet == null) {
            wallet = new MemberWallet();
            wallet.setCoin(coin);
            wallet.setMemberId(member.getId());
            wallet.setBalance(BigDecimal.ZERO);
            wallet.setFrozenBalance(BigDecimal.ZERO);
            setWalletAddress(wallet, coin, member);
            walletService.save(wallet);
        }
    }

    /**
     * Sets the wallet address for the given wallet, coin, and member.
     * If RPC is enabled, attempts to fetch the address from the remote service.
     * Otherwise, sets the address to an empty string.
     * This version avoids nested try/catch blocks for better readability.
     */
    private void setWalletAddress(MemberWallet wallet, Coin coin, Member member) {
        if (coin.getEnableRpc() == BooleanEnum.IS_TRUE) {
            String account = "U" + member.getId();
            String serviceName = "SERVICE-RPC-" + coin.getUnit();
            try {
                String url = String.format("http://%s/rpc/address/{account}", serviceName);
                ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class, account);
                log.info("remote call:service={},result={}", serviceName, result);
                if (result.getStatusCode().value() == 200) {
                    MessageResult mr = result.getBody();
                    if (mr != null && mr.getCode() == 0) {
                        String address = (String) mr.getData();
                        wallet.setAddress(address);
                        sleepWithInterruptHandling(member.getId(), coin.getUnit());
                        return;
                    }
                }
            } catch (Exception e) {
                log.error("call {} failed,error={}", serviceName, e.getMessage());
            }
            wallet.setAddress("");
        } else {
            wallet.setAddress("");
        }
    }

    /**
     * Sleeps for 10 milliseconds and handles InterruptedException properly.
     * Extracted to avoid nested try/catch blocks in setWalletAddress.
     */
    private void sleepWithInterruptHandling(Long memberId, String coinUnit) {
        try {
            Thread.sleep(10L);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            log.warn("Thread interrupted while setting wallet address for memberId={}, coin={}", memberId, coinUnit, ie);
        }
    }

    @Override
    public MessageResult needCreateWallet(String coinName) {
        Coin coin = coinService.findOne(coinName);
        if (coin == null) {
            return MessageResult.error("Currency configuration does not exist");
        }
        MessageResult result = success("", false);
        List<Member> list = memberService.findAll();
        for (Member member : list) {
            MemberWallet wallet = memberWalletService.findByCoinAndMember(coin, member);
            if (wallet == null) {
                result = success(true);
                return result;
            }
        }
        return result;
    }

    @Override
    public MessageResult getKey(String phone) {
        String key = SysConstant.ADMIN_COIN_TRANSFER_COLD_PREFIX + phone + "_PASS";
        @SuppressWarnings("rawtypes")
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object object = valueOperations.get(key);
        if (object == null) {
            return error(messageSource.getMessage("NEED_CODE"));
        }
        return success(messageSource.getMessage("NO_NEED_CODE"), object);
    }

    @Override
    public MessageResult addPartner(String coinId, long amount, long memberId) {
        BigDecimal init = BigDecimal.ZERO;
        MemberTransaction memberTransaction = new MemberTransaction();
        memberTransaction.setMemberId(memberId);
        memberTransaction.setAmount(BigDecimal.valueOf(amount));
        memberTransaction.setCreateTime(new Date());
        memberTransaction.setFee(init);
        memberTransaction.setFlag(0);
        memberTransaction.setSymbol("BHB");
        memberTransaction.setType(TransactionType.RECHARGE);
        memberTransaction.setRealFee("0");
        memberTransaction.setDiscountFee("0");

        MemberTransaction result = memberTransactionService.save(memberTransaction);

        int resultWallet = walletService.updateByMemberIdAndCoinId(memberId, "BHB", BigDecimal.valueOf(amount));
        if (resultWallet == 1) {
            log.info("The balance is modified successfully -- memberID:" + memberId + "amount:" + amount);
        } else {
            log.info("The balance is modified successfully");
        }
        if (result != null) {


            log.info("Adding the database successfully --memberID:" + memberId + "amount:" + amount);
        } else {
            log.info("Failed to add database");
        }

        return success();
    }

    /**
     * Retrieves a paginated list of token listings based on the provided filter criteria.
     * <p>
     * Queries the database for coins matching the filters in {@code tokenListRequest}, fetches current price and 24h volume
     * from the market service, and transforms the results into {@code TokenListingDTO} objects. Returns the data along with
     * pagination details in a {@code MessageResult}.
     *
     * @param pageModelRequest the pagination model containing page number and size
     * @param tokenListRequest the filter criteria for token listing (name, symbol, market type, status)
     * @return MessageResult containing a paginated list of token listings and related metadata, or an error message if the operation fails
     */
    @Override
    public MessageResult getTokenListingData(PageModel pageModelRequest, TokenListRequest tokenListRequest) {
        try {
            // Build query criteria
            Criteria<Coin> criteria = buildTokenListCriteria(tokenListRequest);
            PageModel newPageModel = getPageModel(pageModelRequest, tokenListRequest);

            Page<Coin> coinPageResult = coinService.findAll(criteria, newPageModel.getPageable());

            // Transform coins to token listing format
            List<TokenListingResponse> tokenListings = new ArrayList<>();

            // Get all coin thumbs from market service using the extracted method
            List<CoinThumb> coinThumbs = fetchCoinThumbsFromMarketService();

            // Build a map for quick lookup: symbol -> CoinThumb
            Map<String, CoinThumb> coinThumbMap = new HashMap<>();
            for (CoinThumb coinThumb : coinThumbs) {
                coinThumbMap.put(coinThumb.getSymbol(), coinThumb);
            }

            // Get all contract symbols from future service using the extracted method
            List<ContractSymbolDTO> contractSymbols = fetchContractSymbolsFromFutureService();

            // Build a map for quick lookup: symbol -> ContractSymbolDTO
            Map<String, ContractSymbolDTO> contractSymbolMap = new HashMap<>();
            for (ContractSymbolDTO contractSymbol : contractSymbols) {
                contractSymbolMap.put(contractSymbol.getSymbol(), contractSymbol);
            }

            for (Coin coin : coinPageResult.getContent()) {
                BigDecimal currentPrice = BigDecimal.ZERO;
                BigDecimal volume24h = BigDecimal.ZERO;

                // Create the symbol key for lookup in Map
                String symbolKey = coin.getUnit() + "/USDT";
                // Get current price and 24h volume from market service
                CoinThumb coinThumb = coinThumbMap.get(symbolKey);
                if (coinThumb != null) {
                    currentPrice = coinThumb.getClose();
                    volume24h = coinThumb.getVolume();
                }

                // Get future trading status from enabled symbols list
                Boolean futureTradingStatus = contractSymbolMap.containsKey(symbolKey);
                // Filter by future trading status
                if (tokenListRequest.getFutureTradingStatus() != null
                        && !futureTradingStatus.equals(tokenListRequest.getFutureTradingStatus()))
                    continue;

                Integer status = coin.getStatus() == CommonStatus.NORMAL ? 0 : 1;

                TokenListingResponse tokenData = TokenListingResponse.builder()
                        .iconUrl(coin.getIconUrl())
                        .tokenName(coin.getName())
                        .symbol(coin.getUnit())
                        .listingTime(coin.getCreateTime())
                        .price(currentPrice)
                        .volume24h(volume24h)
                        .status(status)
                        .spotTradingStatus(coin.getEnableSpotTrading())
                        .futureTradingStatus(futureTradingStatus)
                        .build();

                tokenListings.add(tokenData);
            }

            int pageNumber = coinPageResult.getNumber();
            long pageSize = coinPageResult.getSize();
            long totalElements = coinPageResult.getTotalElements();
            long totalPages = coinPageResult.getTotalPages();
            boolean last = coinPageResult.isLast();

            List<TokenListingResponse> resultTokenListings = tokenListings;

            // Pagination when future trading status is provided
            if (tokenListRequest.getFutureTradingStatus() != null) {
                pageNumber = pageModelRequest.getPageNo() - 1;
                pageSize = pageModelRequest.getPageSize();
                totalElements = tokenListings.size();
                totalPages = (int) Math.ceil((double) totalElements / pageSize);
                last = pageNumber >= totalPages;

                resultTokenListings = tokenListings.stream()
                        .skip(pageNumber * pageSize)
                        .limit(pageModelRequest.getPageSize())
                        .toList();
            }

            // Create response with pagination info
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("content", resultTokenListings);
            result.put("pageNumber", pageNumber);
            result.put("pageSize", pageSize);
            result.put("totalElements", totalElements);
            result.put("totalPages", totalPages);
            result.put("last", last);

            return success(result);
        } catch (ServiceInterruptException e) {
            log.error("ServiceInterruptException fetching token listing data", e);
            throw e;
        } catch (Exception e) {
            log.error("Error fetching token listing data", e);
            return error("Failed to fetch token listing data");
        }
    }

    private static @NotNull PageModel getPageModel(PageModel pageModelRequest, TokenListRequest tokenListRequest) {
        // Set default sorting if not provided
        if (pageModelRequest.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("createTime");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModelRequest.setProperty(list);
            pageModelRequest.setDirection(directions);
        }
        PageModel newPageModel = new PageModel();
        // Take all if futureTradingStatus is provided
        if (tokenListRequest.getFutureTradingStatus() != null) {
            newPageModel.setPageNo(1);
            newPageModel.setPageSize(Integer.MAX_VALUE);
        } else {
            newPageModel.setPageNo(pageModelRequest.getPageNo());
            newPageModel.setPageSize(pageModelRequest.getPageSize());
        }
        newPageModel.setProperty(pageModelRequest.getProperty());
        newPageModel.setDirection(pageModelRequest.getDirection());
        return newPageModel;
    }

    /**
     * Builds a Criteria object for querying Coin entities based on the provided TokenListRequest filters.
     * <p>
     * Adds conditions for name (partial match), symbol (partial match), and status (mapped to CommonStatus).
     * Market type filtering is not implemented.
     *
     * @param tokenListRequest the filter parameters for token listing
     * @return Criteria<Coin> with applied filters
     */
    private Criteria<Coin> buildTokenListCriteria(TokenListRequest tokenListRequest) {
        Criteria<Coin> criteria = new Criteria<>();

        if (StringUtils.isNotBlank(tokenListRequest.getName())) {
            criteria.add(Restrictions.like("name", "%" + tokenListRequest.getName() + "%", false));
        }
        if (StringUtils.isNotBlank(tokenListRequest.getSymbol())) {
            criteria.add(Restrictions.like("unit", "%" + tokenListRequest.getSymbol() + "%", false));
        }
        if (tokenListRequest.getSpotTradingStatus() != null) {
            criteria.add(Restrictions.eq("enableSpotTrading", tokenListRequest.getSpotTradingStatus(), false));
        }
        if (tokenListRequest.getStatus() != null) {
            int status = tokenListRequest.getStatus();
            criteria.add(Restrictions.eq("status", status == 0 ? CommonStatus.NORMAL : CommonStatus.ILLEGAL, false));
        }

        return criteria;
    }

    /**
     * Fetches coin thumbs from the market service.
     *
     * @return List of CoinThumb or null if failed
     */
    private List<CoinThumb> fetchCoinThumbsFromMarketService() throws ServiceInterruptException {
        try {
            String marketUrl = "http://" + marketServiceName + "/market/symbol-thumb";
            ResponseEntity<List<CoinThumb>> result = restTemplate.exchange(
                    marketUrl,
                    org.springframework.http.HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<>() {
                    }
            );
            return result.getBody();
        } catch (Exception e) {
            log.error("Failed to get coin thumbs from market service", e);
            throw new ServiceInterruptException("Failed to get coin thumbs from market service", e);
        }
    }

    private List<ContractSymbolDTO> fetchContractSymbolsFromFutureService() throws ServiceInterruptException {
        try {
            String futureUrl = "http://" + futureApiServiceName + "/future-api/api/v1/contracts/enabled";
            ResponseEntity<FutureServiceResponse<List<ContractSymbolDTO>>> result = restTemplate.exchange(
                    futureUrl,
                    org.springframework.http.HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<>() {
                    }
            );
            FutureServiceResponse<List<ContractSymbolDTO>> futureServiceResponse = result.getBody();
            if (futureServiceResponse != null) {
                if (futureServiceResponse.getCode() != 200) {
                    throw new ServiceInterruptException("Failed to get contract symbols from future service");
                }
                return futureServiceResponse.getData();
            } else {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Failed to get contract symbols from future service", e);
            throw new ServiceInterruptException("Failed to get contract symbols from future service", e);
        }
    }

    /**
     * Retrieves token information from a smart contract on the specified network.
     *
     * @param tokenContractRequest the request containing network protocol and contract address
     * @return MessageResult containing token information or an error message
     */
    @Override
    public MessageResult getTokenContract(TokenContractRequest tokenContractRequest) {
        try {
            String networkProtocol = tokenContractRequest.getNetworkProtocol();
            String contractAddress = tokenContractRequest.getContractAddress();

            // Retrieve the coin protocol from database for the specified network
            Coinprotocol coinprotocol = coinprotocolService.findByProtocol(Integer.valueOf(networkProtocol));
            if (coinprotocol == null) {
                log.error("Unsupported network protocol: {}", networkProtocol);
                return error(messageSource.getMessage("UNSUPPORTED_NETWORK"));
            }

            // Initialize Web3j instance for the specified network
            Web3j web3j = Web3j.build(new HttpService(coinprotocol.getRpcserver()));

            // Query the smart contract for token name, symbol, decimals, and total supply
            String name = getTokenContractParam(web3j, contractAddress, MethodTokenConstants.METHOD_NAME);
            if (StringUtils.isBlank(name)) {
                log.error("Invalid contract address: {}", contractAddress);
                return error(messageSource.getMessage("INVALID_CONTRACT_ADDRESS"));
            }
            String symbol = getTokenContractParam(web3j, contractAddress, MethodTokenConstants.METHOD_SYMBOL);
            if (StringUtils.isBlank(symbol)) {
                log.error("Invalid contract address: {}", contractAddress);
                return error(messageSource.getMessage("INVALID_CONTRACT_ADDRESS"));
            }
            int decimals = Integer.parseInt(getTokenContractParam(web3j, contractAddress, MethodTokenConstants.METHOD_DECIMALS));
            BigInteger totalSupplyRaw = new BigInteger(getTokenContractParam(web3j, contractAddress, MethodTokenConstants.METHOD_TOTAL_SUPPLY));
            // Adjust total supply by dividing by 10^decimals to get the human-readable value
            BigDecimal totalSupply = new BigDecimal(totalSupplyRaw).divide(BigDecimal.TEN.pow(decimals));

            // Build and return a successful response with the token info
            return success(TokenContractResponse.builder()
                    .networkProtocol(networkProtocol)
                    .contractAddress(contractAddress)
                    .name(name)
                    .symbol(symbol)
                    .totalSupplyRaw(totalSupplyRaw)
                    .totalSupply(totalSupply)
                    .decimals(decimals)
                    .build()
            );

        } catch (Exception e) {
            log.error("Error fetching token info", e);
            return error(messageSource.getMessage("FAILED_TO_FETCH_TOKEN_CONTRACT"));
        }
    }

    /**
     * Calls a specified ERC-20 contract method (name, symbol, decimals, or totalSupply) using Web3j,
     * decodes the result, and returns it as a string.
     *
     * @param web3j      the Web3j instance for blockchain interaction
     * @param contract   the contract address to query
     * @param methodName the ERC-20 method name ("name", "symbol", "decimals", or "totalSupply")
     * @return the decoded method result as a string, or a default value if decoding fails
     * @throws IOException              if the contract call fails
     * @throws IllegalArgumentException if the method name is unknown
     */
    private String getTokenContractParam(Web3j web3j, String contract, String methodName) throws IOException, IllegalArgumentException {
        Function function = getFunction(methodName);

        String value = web3Service.callFunction(web3j, contract, function);
        @SuppressWarnings("rawtypes")
        List<Type> decoded = FunctionReturnDecoder.decode(value, function.getOutputParameters());

        return switch (methodName) {
            case MethodTokenConstants.METHOD_NAME, MethodTokenConstants.METHOD_SYMBOL ->
                    decoded.isEmpty() ? "" : decoded.get(0).getValue().toString();
            case MethodTokenConstants.METHOD_DECIMALS ->
                    (decoded.isEmpty() ? "0" : ((Uint8) decoded.get(0)).getValue().intValue()).toString();
            case MethodTokenConstants.METHOD_TOTAL_SUPPLY ->
                    (decoded.isEmpty() ? "0" : ((Uint256) decoded.get(0)).getValue()).toString();
            default ->
                    throw new IllegalArgumentException(messageSource.getMessage("UNKNOWN_METHOD_NAME", new Object[]{methodName}));
        };
    }

    /**
     * Constructs a Web3j Function object for the specified ERC-20 method name.
     * <p>
     * Supports "name", "symbol", "decimals", and "totalSupply" methods, mapping each to the appropriate output type.
     * Throws IllegalArgumentException for unsupported method names.
     *
     * @param methodName the ERC-20 method name ("name", "symbol", "decimals", or "totalSupply")
     * @return a Function object configured for the specified method
     * @throws IllegalArgumentException if the method name is unknown
     */
    private Function getFunction(String methodName) throws IllegalArgumentException {
        TypeReference<?> typeReference = switch (methodName) {
            case MethodTokenConstants.METHOD_NAME, MethodTokenConstants.METHOD_SYMBOL ->
                    new TypeReference<Utf8String>() {
                    };
            case MethodTokenConstants.METHOD_DECIMALS -> new TypeReference<Uint8>() {
            };
            case MethodTokenConstants.METHOD_TOTAL_SUPPLY -> new TypeReference<Uint256>() {
            };
            default ->
                    throw new IllegalArgumentException(messageSource.getMessage("UNKNOWN_METHOD_NAME", new Object[]{methodName}));
        };
        return new Function(
                methodName,
                Collections.emptyList(),
                List.of(typeReference)
        );
    }

    /**
     * Saves a new token to the database based on the provided token information.
     * <p>
     * Validates the network protocol and checks for existing tokens with the same contract address.
     * If valid and not existing, creates new Coin and Coinext records with default values and persists them.
     * Returns a success or error MessageResult based on the operation outcome.
     *
     * @param saveTokenRequest the token information to be saved
     * @return MessageResult indicating success or failure of the save operation
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageResult saveToken(SaveTokenRequest saveTokenRequest) {
        try {
            // List for easy query token contract requests
            List<TokenContractRequest> tokenContractRequests = saveTokenRequest.getTokenContracts();
            // Map to store contract address and network protocol for quick validate contract address
            Map<String, String> contractNetworkMap = new HashMap<>();
            // List to store valid token contracts
            List<TokenContractResponse> validTokenContracts = new ArrayList<>();
            // Validate token contract requests
            MessageResult validationResult = validateTokenContractRequests(tokenContractRequests, contractNetworkMap, validTokenContracts);
            if (validationResult.getCode() != Constants.MESSAGE_RESULT_SUCCESS_CODE) {
                return validationResult;
            }

            // Check if the token already exists in the database
            Coin coin = coinService.findByName(saveTokenRequest.getTokenName());
            if (coin != null) {
                log.error("Token already exists: {}", saveTokenRequest.getTokenName());
                return error(messageSource.getMessage("THIS_COIN_HAS_EXISTED"));
            }
            // Save token info to database
            Coin newCoin = getNewCoin(saveTokenRequest);
            Coin savedCoin = coinService.save(newCoin);

            // Save token contract to Coinext
            this.saveTokenContract(validTokenContracts, savedCoin, contractNetworkMap);

            // Create new pair with the saved coin as base coin and USDT as quote coin
            Coin usdtCoin = coinService.findByUnit(Constants.SYMBOL_DEFAULT_QUOTE_COIN);
            pairManagementService.createNewPair(savedCoin, usdtCoin);

            return success();
        }
        catch (BadRequestException ex) {
            log.error("Error saving token info, bad request", ex);
            throw ex;
        }
        catch (Exception ex) {
            log.error("Error saving token info", ex);
            throw new AdminInternalException(messageSource.getMessage(MESSAGE_FAILED_SAVE_TOKEN));
        }
    }

    /**
     * Validates token contract requests and fetches contract details from blockchain
     *
     * @param tokenContractRequests List of token contract requests to validate
     * @param contractNetworkMap    Map to store contract addresses and their network protocols
     * @param validTokenContracts   List to store valid token contracts
     * @return MessageResult indicating success or error
     */
    private MessageResult validateTokenContractRequests(List<TokenContractRequest> tokenContractRequests,
                                                        Map<String, String> contractNetworkMap,
                                                        List<TokenContractResponse> validTokenContracts) {
        for (TokenContractRequest tokenContractRequest : tokenContractRequests) {
            // Validate contract address has not been used in another network protocol
            if (contractNetworkMap.containsKey(tokenContractRequest.getContractAddress())) {
                log.error("Token contract address {} has been used in another network protocol",
                        tokenContractRequest.getContractAddress());
                return error(messageSource.getMessage("TOKEN_CONTRACT_ADDRESS_HAS_BEEN_USED"));
            }
            contractNetworkMap.put(tokenContractRequest.getContractAddress(), tokenContractRequest.getNetworkProtocol());

            // Fetch token contract from the blockchain to validate TokenContractRequest
            MessageResult tokenContractResult = getTokenContract(tokenContractRequest);
            if (tokenContractResult.getCode() != Constants.MESSAGE_RESULT_SUCCESS_CODE) {
                return tokenContractResult;
            }

            TokenContractResponse tokenContractResponse = (TokenContractResponse) tokenContractResult.getData();
            if (tokenContractResponse == null) {
                log.error("Failed to fetch token contract for contract address: {}",
                        tokenContractRequest.getContractAddress());
                return error(messageSource.getMessage("FAILED_TO_FETCH_TOKEN_CONTRACT"));
            }
            validTokenContracts.add(tokenContractResponse);
        }

        return success();
    }

    /**
     * Constructs a new Coinext entity with the specified token contract and protocol.
     * <p>
     * Initializes the Coinext entity with the token name, symbol, protocol, contract address, decimals, and total supply
     * from the provided token contract. Sets the status to active (1) and other fields to default values.
     *
     * @param savedCoin     the Coin entity associated with the token
     * @param tokenContract the TokenContractResponse containing token contract details
     * @param coinprotocol  the Coinprotocol entity specifying the protocol details
     * @return a new Coinext entity initialized with the provided token contract and protocol
     */
    private static @NotNull Coinext getNewCoinext(Coin savedCoin, TokenContractResponse tokenContract, Coinprotocol coinprotocol) {
        Coinext newCoinext = new Coinext();
        newCoinext.setCoinname(tokenContract.getName()); // foreign key
        newCoinext.setCoinSymbol(tokenContract.getSymbol());
        newCoinext.setProtocol(coinprotocol.getProtocol()); // foreign key
        newCoinext.setProtocolname(coinprotocol.getProtocolname());
        newCoinext.setExt(tokenContract.getContractAddress());
        newCoinext.setDecimals(tokenContract.getDecimals());
        newCoinext.setTotalSupply(tokenContract.getTotalSupplyRaw()); // save raw value
        newCoinext.setStatus(1);

        // Set other fields to default values
        newCoinext.setCoinid(savedCoin.getName());
        newCoinext.setWithdrawfee(0.00);
        newCoinext.setMinwithdrawfee(0.00);
        newCoinext.setIswithdraw(true);
        newCoinext.setIsrecharge(true);
        newCoinext.setIsautowithdraw(true);
        newCoinext.setMinwithdraw(BigDecimal.ZERO);
        newCoinext.setMaxwithdraw(BigDecimal.ZERO);
        newCoinext.setMinrecharge(BigDecimal.ZERO);
        newCoinext.setConfirms(0);
        return newCoinext;
    }

    /**
     * Constructs a new Coin entity with the specified token request.
     * <p>
     * Initializes the Coin entity with the token name, symbol, image, description, circulation supply, and max supply
     * from the provided token request. Sets other fields to default values.
     *
     * @param saveTokenRequest the SaveTokenRequest containing token details
     * @return a new Coin entity initialized with the provided token request
     */
    private static @NotNull Coin getNewCoin(SaveTokenRequest saveTokenRequest) {
        Coin newCoin = new Coin();
        newCoin.setName(saveTokenRequest.getTokenName()); // primary key
        newCoin.setUnit(saveTokenRequest.getTokenSymbol());
        newCoin.setIconUrl(saveTokenRequest.getTokenImage());
        newCoin.setDescription(saveTokenRequest.getTokenDescription());
        newCoin.setCirculationSupply(saveTokenRequest.getCirculationSupply());
        newCoin.setMaxSupply(saveTokenRequest.getMaxSupply());

        // Set other fields to default values
        newCoin.setWithdrawThreshold(BigDecimal.ZERO);
        newCoin.setCanAutoWithdraw(BooleanEnum.IS_FALSE);
        newCoin.setCanRecharge(BooleanEnum.IS_FALSE);
        newCoin.setCanWithdraw(BooleanEnum.IS_FALSE);

        newCoin.setMinWithdrawAmount(BigDecimal.ZERO);
        newCoin.setMaxWithdrawAmount(BigDecimal.ZERO);
        newCoin.setMinRechargeAmount(BigDecimal.ZERO);
        newCoin.setMinTxFee(0.00);
        newCoin.setMaxTxFee(0.00);
        newCoin.setEnableRpc(BooleanEnum.IS_FALSE);
        newCoin.setAccountType(0);
        newCoin.setCoinScale(0);
        return newCoin;
    }

    /**
     * Get detailed information of a coin by its name.
     *
     * <p>The method will:</p>
     * <ul>
     *   <li>Check if the coin exists in the system.</li>
     *   <li>Check if extra coin information exists (Coinext).</li>
     *   <li>Fetch all contract symbols from the future service to check if the coin has future trading enabled.</li>
     *   <li>Build a {@link TokenDetailResponse} with coin data, supply info, trading status, and other details.</li>
     * </ul>
     *
     * @param name coin name
     * @return {@link MessageResult} containing coin detail if found,
     * or error message if coin does not exist
     */
    @Override
    public MessageResult getTokenDetail(String name) {

        Coin coin = coinService.findByName(name);
        if (coin == null) {
            return MessageResult.error(MESSAGE_COIN_NAME_NOT_EXIST);
        }
        // Get all contract symbols from future service using the extracted method
        List<ContractSymbolDTO> contractSymbols = fetchContractSymbolsFromFutureService();
        String symbolKey = coin.getUnit() + "/USDT";

        boolean futureTradingStatus = contractSymbols.stream()
                .anyMatch(cs -> cs.getSymbol().equals(symbolKey));

        List<Coinext> coinexts = coinextService.findByCoinId(coin.getName());
        List<TokenContractResponse> tokenContracts = coinexts.stream()
                .map(c -> TokenContractResponse.builder()
                        .networkProtocol(c.getProtocol().toString())
                        .contractAddress(c.getExt())
                        .name(c.getCoinname())
                        .symbol(c.getCoinSymbol())
                        .totalSupply(new BigDecimal(c.getTotalSupply()))
                        .decimals(c.getDecimals())
                        .build())
                .toList();

        String imageName;
        try {
            imageName = extractFileName(coin.getIconUrl());
        } catch (Exception e) {
            return MessageResult.error("Invalid icon URL: ");
        }

        TokenDetailResponse tokenDetailResponse = TokenDetailResponse.builder()
                .circulationSupply(coin.getCirculationSupply())
                .imageUrl(coin.getIconUrl())
                .imageName(imageName)
                .description(coin.getDescription())
                .spotTradingStatus(coin.getEnableSpotTrading())
                .futureTradingStatus(futureTradingStatus)
                .maxSupply(coin.getMaxSupply())
                .tokenName(coin.getName())
                .tokenSymbol(coin.getUnit())
                .contracts(tokenContracts)
                .build();

        return MessageResult.success(tokenDetailResponse);
    }

    public String extractFileName(String urlStr) throws URISyntaxException {
        URI uri = new URI(urlStr);
        return FilenameUtils.getName(uri.getPath());
    }

    /**
     * Edit token information and update its associated contract addresses.
     * <p>
     * This method performs the following steps:
     * <ul>
     *     <li>Fetches the {@link Coin} entity by its current name.</li>
     *     <li>Validates if the token exists; if not, returns an error.</li>
     *     <li>Validates the new contract addresses from the request:
     *      *         <ul>
     *      *             <li>Ensures contract addresses follow supported protocols.</li>
     *      *             <li>Prevents duplicate contracts under the same token.</li>
     *      *         </ul>
     *      *     </li>
     *     <li>Deletes all existing {@link Coinext} contract records linked to the token.</li>
     *     <li>Updates the token’s metadata such as name, symbol, image, supply, and description.</li>
     *     <li>If validation passes, saves new {@link Coinext} entries for each provided contract.</li>
     * </ul>
     * <p>
     * The entire operation runs inside a transaction. If any validation fails or an exception is thrown,
     * the transaction will roll back, leaving the token and its contracts unchanged.
     *
     * @param tokenName the current name of the token to edit
     * @param request the {@link SaveTokenRequest} containing new token metadata and contract addresses
     * @return {@link MessageResult} indicating success or failure of the update
     */
    @Override
    @Transactional
    public MessageResult editTokenDetail(String tokenName, EditTokenRequest request) {
        try {
            log.info("Beginning process for editing token, check token name {} and request {}", tokenName , request);
            Coin coin = coinService.findByName(tokenName);
            if (coin == null) {
                log.error("The token with name: {} doesn't exist", tokenName);
                return MessageResult.error(messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
            }

        //Validate contract address
            // List for easy query token contract requests
            List<TokenContractRequest> tokenContractRequests = request.getTokenContracts();
            // Map to store contract address and network protocol for quick validate contract address
            Map<String, String> contractNetworkMap = new HashMap<>();
            // List to store valid token contracts
            List<TokenContractResponse> validTokenContracts = new ArrayList<>();
            // Validate token contract requests
            MessageResult validationResult = validateTokenContractRequests(tokenContractRequests, contractNetworkMap, validTokenContracts);
            if (validationResult.getCode() != 0) {
                return validationResult;
            }

            //Delete all exist contract address of The Token
            List<Coinext> coinextList = coinextService.findByCoinId(coin.getName());
            List<String> coinIds = coinextList.stream()
                    .map(Coinext::getCoinid)
                    .toList();
            coinextService.deleteByCoinIdIn(coinIds);

            //Handle edit token
            coin.setIconUrl(request.getTokenImage());
            BigDecimal circulationSupply = request.getCirculationSupply();
            if(circulationSupply != null) {
                coin.setCirculationSupply(circulationSupply);
            }

            BigDecimal maxSupply = request.getMaxSupply();
            if(maxSupply != null) {
                coin.setMaxSupply(request.getMaxSupply());
            }

            String description = request.getTokenDescription();
            if (description != null) {
                coin.setDescription(request.getTokenDescription());
            }

            coinService.save(coin);

            // Save token contract address to Coinext
            this.saveTokenContract(validTokenContracts, coin, contractNetworkMap);

        } catch (Exception e) {
            log.error("Failed to save token: {}", e.getMessage());
            return MessageResult.error(e.getMessage());
        }
        return MessageResult.success("Update token successfully");
    }

    /**
     * Save valid token contract information into the database.
     * <p>
     * For each provided {@link TokenContractResponse}, this method:
     * <ul>
     *     <li>Validates the network protocol against existing {@link Coinprotocol} records.</li>
     *     <li>Checks for duplicate contracts by coin name and protocol.</li>
     *     <li>If valid and not existing, creates and persists a new {@link Coinext} entry linked to the provided {@link Coin}.</li>
     * </ul>
     * <p>
     * If any validation fails (unsupported protocol or existing contract), an exception is thrown to trigger rollback.
     *
     * @param validTokenContracts list of valid token contracts to save
     * @param coin the {@link Coin} entity to which the contracts belong
     * @param contractNetworkMap mapping of contract addresses to their network protocols
     * @throws BadRequestException if protocol is unsupported or contract already exists
     */
    private void saveTokenContract(List<TokenContractResponse> validTokenContracts, Coin coin, Map<String, String> contractNetworkMap) {
        for (TokenContractResponse validTokenContract : validTokenContracts) {
            // Retrieve the coin protocol from database for the specified network
            Coinprotocol coinprotocol = coinprotocolService.findByProtocol(Integer.valueOf(validTokenContract.getNetworkProtocol()));
            if (coinprotocol == null) {
                log.error("Unsupported network protocol: {}", validTokenContract.getNetworkProtocol());
                // Throw exception to roll back the transaction
                throw new BadRequestException(messageSource.getMessage("UNSUPPORTED_NETWORK"));
            }

            // Check if the token contract already exists in the database
            Coinext coinext = coinextService.findFirstByCoinnameAndProtocol(validTokenContract.getName(), coinprotocol.getProtocol());
            if (coinext != null) {
                log.error("Token already exists: {} in protocol: {}", validTokenContract.getName(), validTokenContract.getNetworkProtocol());
                // Throw exception to roll back the transaction
                throw new BadRequestException(messageSource.getMessage("TOKEN_CONTRACT_ADDRESS_HAS_BEEN_USED"));
            }

            // Save token contract to Coinext
            Coinext newCoinext = getNewCoinext(coin, validTokenContract, coinprotocol);
            coinextService.save(newCoinext);

            log.info("Successfully saved contract: {} with address: {} on network: {}", validTokenContract.getName(), validTokenContract.getContractAddress(), validTokenContract.getNetworkProtocol());
        }

        log.info("Successfully saved token: {} with all contract addresses: {}", coin.getName(), contractNetworkMap.keySet());
    }
}

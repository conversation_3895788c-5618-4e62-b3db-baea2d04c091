package com.icetea.lotus.service.system;


import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.RoleDto;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedSysRoleService {
    MessageResult mergeRole(RoleDto roleDto);

    MessageResult allMenu();

    MessageResult roleAllPermission(Long roleId);

    MessageResult getAllRole(PageModel pageModel);

    MessageResult deletes(Long id);
}

package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.SysPermission;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedSysPermissionService {
    MessageResult merge(SysPermission permission);

    MessageResult pageQuery(PageModel pageModel, Long parentId);

    MessageResult detail(Long id);

    MessageResult deletes(Long[] ids);
}

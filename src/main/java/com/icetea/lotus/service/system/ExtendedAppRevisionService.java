package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.create.AppRevisionCreate;
import com.icetea.lotus.model.update.AppRevisionUpdate;
import com.icetea.lotus.util.MessageResult;


public interface ExtendedAppRevisionService {
    MessageResult create(AppRevisionCreate model);

    MessageResult put(Long id, AppRevisionUpdate model);

    MessageResult get(Long id);

    MessageResult get(PageModel pageModel);
}

package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.TransferAddress;
import com.icetea.lotus.model.screen.TransferAddressScreen;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedTransferAddressService {
    MessageResult merge(TransferAddress transferAddress, String coinName);

    MessageResult pageQuery(PageModel pageModel, TransferAddressScreen transferAddressScreen);

    MessageResult detail(Long id);

    MessageResult deletes(Long[] ids);
}

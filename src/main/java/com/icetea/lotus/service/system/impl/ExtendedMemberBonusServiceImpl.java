package com.icetea.lotus.service.system.impl;

import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.MemberBonusDTO;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.service.MemberBonusService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedMemberBonusService;
import com.icetea.lotus.util.MessageResult;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * The type Extended member bonus service.
 */
@Slf4j
@Service
public class ExtendedMemberBonusServiceImpl extends BaseAdminController implements ExtendedMemberBonusService {

    private final MemberBonusService memberBonusService;

    private final MemberService memberService;

    public ExtendedMemberBonusServiceImpl(BaseAdminService baseAdminService, MemberBonusService memberBonusService, MemberService memberService) {
        super(baseAdminService);
        this.memberBonusService = memberBonusService;
        this.memberService = memberService;
    }

    @Override
    public MessageResult findAllCondition(Long memberId, String phone, Integer pageNo, Integer pageSize) {
        if (memberId!=null) {
            log.info("==Query all dividend records based on memberId=== memberId:" + memberId);
            return findByMemberId(memberId,pageNo-1,pageSize);
        }
        if (!StringUtils.isEmpty(phone)) {
            log.info("==Check all dividend records based on phone===phone:" +phone);
            Member member=memberService.findByPhone(phone);
            return findByMemberId(member.getId(),pageNo-1,pageSize);
        }
        if (memberId==null&&StringUtils.isEmpty(phone)) {
            log.info("===== Query all dividend records=======" );
            Page<MemberBonusDTO> page = memberBonusService.getMemberBounsPage(pageNo-1,pageSize);

            return  success(page);
        }
        return success();
    }

    /**
     * Find by member id message result.
     *
     * @param memberId the member id
     * @param pageNo   the page no
     * @param pageSize the page size
     * @return the message result
     */
// Dividend query based on memberId
    public MessageResult findByMemberId(Long memberId,Integer pageNo,Integer pageSize){
        Page<MemberBonusDTO> page=memberBonusService.getBonusByMemberIdPage(memberId,pageNo,pageSize);
        log.info("id query result:"+page.getContent());
        return  success(page);
    }
}

package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.LoginRequest;
import com.icetea.lotus.dto.request.UpdatePasswordRequest;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.client.RestTemplate;

public interface EmployeeService {
    MessageResult adminLogin(LoginRequest loginRequest);

    MessageResult doLogin(String username, String password, String phone, String code, boolean rememberMe, HttpServletRequest request);

    MessageResult valiatePhoneCode(HttpServletRequest request);

    MessageResult logout(RestTemplate restTemplate, String refreshToken);

    MessageResult addAdmin(Admin admin, Long departmentId);

    MessageResult findAllAdminUser(PageModel pageModel, String searchKey);

    MessageResult updatePassword(UpdatePasswordRequest updatePasswordRequest);

    MessageResult resetPassword(Long id);

    MessageResult deletes(Long[] ids);

    MessageResult refreshToken(String refreshToken);

    MessageResult myInfo(AuthAdmin authAdmin);

}

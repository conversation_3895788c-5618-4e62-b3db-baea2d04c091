package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.ActivityRewardType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.RewardActivitySetting;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedRewardActivitySettingService {
    MessageResult merge(RewardActivitySetting setting);

    MessageResult pageQuery(PageModel pageModel, ActivityRewardType type);

    MessageResult deletes(Long[] ids);
}

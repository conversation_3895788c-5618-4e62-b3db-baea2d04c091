package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Coinext;
import com.icetea.lotus.model.screen.CoinextScreen;
import com.icetea.lotus.util.MessageResult;

public interface ExtendedCoinextService {
    MessageResult coinList();

    MessageResult protocolList();

    MessageResult pageQuery(PageModel pageModel, CoinextScreen coinextScreen);

    MessageResult merge(Coinext coinext);
}

package com.icetea.lotus.service.system.impl;

import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.AdminAccessLog;
import com.icetea.lotus.entity.spot.QAdmin;
import com.icetea.lotus.entity.spot.QAdminAccessLog;
import com.icetea.lotus.service.AdminAccessLogService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedAdminAccessLogService;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.util.Assert.notNull;


/**
 * The type Extended admin access log service.
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class ExtendedAdminAccessLogServiceImpl extends BaseAdminController implements ExtendedAdminAccessLogService {

    private final AdminAccessLogService adminAccessLogService;

    public ExtendedAdminAccessLogServiceImpl(BaseAdminService baseAdminService, AdminAccessLogService adminAccessLogService) {
        super(baseAdminService);
        this.adminAccessLogService = adminAccessLogService;
    }

    /**
     * Get all admin activities logs.
     *
     * @return A MessageResult containing all admin access logs.
     */
    @Override
    public MessageResult all() {
        List<AdminAccessLog> adminAccessLogList = adminAccessLogService.queryAll();
        return success(adminAccessLogList);
    }

    /**
     * Get the details of a specific admin access log by its ID.
     *
     * @param id The ID of the admin access log.
     * @return A MessageResult containing the details of the specified access log.
     */
    @Override
    public MessageResult detail(Long id) {
        AdminAccessLog adminAccessLog = adminAccessLogService.queryById(id);
        notNull(adminAccessLog, "validate id!");
        return success(adminAccessLog);
    }

    /**
     * Query paginated admin access logs with optional filters.
     *
     * @param pageModel The pagination and sorting information.
     * @param adminName (Optional) The admin username to filter the logs.
     * @param module (Optional) The system module to filter the logs.
     * @return A MessageResult containing the paginated list of admin access logs.
     */
    @Override
    public MessageResult pageQuery(PageModel pageModel, String adminName, AdminModule module) {
        List<BooleanExpression> list = new ArrayList<>();
        list.add(QAdmin.admin.id.eq(QAdminAccessLog.adminAccessLog.adminId));
        if (StringUtils.hasText(adminName)) {
            list.add(QAdmin.admin.username.like("%" + adminName + "%"));
        }
        if (module != null) {
            list.add(QAdminAccessLog.adminAccessLog.module.eq(module));
        }
        Page<AdminAccessLog> all = adminAccessLogService.page(list, pageModel);
        return success(all);
    }


}

package com.icetea.lotus.service.activity;

import com.icetea.lotus.entity.spot.Activity;
import com.icetea.lotus.model.ActivityRequest;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * The interface Activity mapper.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ActivityMapper {
    /**
     * Update activity from request.
     *
     * @param request  the request
     * @param activity the activity
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateActivityFromRequest(ActivityRequest request, @MappingTarget Activity activity);
}

package com.icetea.lotus.service.activity;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.MemberSignRecordScreen;
import com.icetea.lotus.util.MessageResult;


/**
 * The interface Member sign recorded service.
 */
public interface MemberSignRecordedService {
    /**
     * Page query message result.
     *
     * @param screen    the screen
     * @param pageModel the page model
     * @return the message result
     */
    MessageResult pageQuery(MemberSignRecordScreen screen, PageModel pageModel);
}

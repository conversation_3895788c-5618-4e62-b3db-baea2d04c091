package com.icetea.lotus.service.admin.impl;

import com.icetea.lotus.common.constants.Constants;
import com.icetea.lotus.common.exception.TransferException;
import com.icetea.lotus.constant.AdminTransactionStatus;
import com.icetea.lotus.constant.AdminTransactionType;
import com.icetea.lotus.constant.AdminWalletType;
import com.icetea.lotus.dao.AdminTransactionDao;
import com.icetea.lotus.dao.AdminWalletDao;
import com.icetea.lotus.dto.request.AdminWalletListRequest;
import com.icetea.lotus.dto.request.AdminWalletTransferRequest;
import com.icetea.lotus.dto.request.WalletTransactionHistoryRequest;
import com.icetea.lotus.dto.response.AdminWalletTransactionHistoryResponse;
import com.icetea.lotus.dto.response.ListingAdminWalletResponse;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.AdminTransaction;
import com.icetea.lotus.entity.spot.AdminWallet;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.Coinext;
import com.icetea.lotus.entity.spot.QAdminTransaction;
import com.icetea.lotus.entity.spot.QAdminWallet;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.AdminService;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinextService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.admin.AdminWalletManagementService;
import com.icetea.lotus.util.GeneratorUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DateTimeException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminWalletManagementServiceImpl implements AdminWalletManagementService {
    private final AdminWalletDao adminWalletDao;
    private final AdminTransactionDao adminTransactionDao;
    private final AdminService adminService;
    private final LocaleMessageSourceService messageSource;
    private final CoinService coinService;
    private final CoinextService coinextService;

    @Value("${admin.wallet.fiat-coins:USDT}")
    private List<String> fiatCoins;
    @Value("${admin.wallet.max-transfer-amount}")
    private BigDecimal maxTransferAmount;

    /**
     * Get list of admin wallet
     *
     * @param screen Filter screen
     * @return List of admin wallet with pagination
     */
    @Override
    public Page<ListingAdminWalletResponse> listingWallet(AdminWalletListRequest screen) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        String walletName = screen.getWalletName();
        if (!StringUtils.isBlank(walletName)) {
            booleanExpressions.add(QAdminWallet.adminWallet.walletType.eq(AdminWalletType.fromDisplayName(walletName)));
        }

        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);
        int pageNo = screen.getPageNo();
        int pageSize = screen.getPageSize();
        Pageable pageable = PageRequest.of(pageNo > 0 ? pageNo - 1 : 0, pageSize);

        Page<AdminWallet> allWalletByUserAdmin = adminWalletDao.findAll(predicate, pageable);

        return allWalletByUserAdmin.map(v -> {
            ListingAdminWalletResponse walletResponse = new ListingAdminWalletResponse();
            BeanUtils.copyProperties(v, walletResponse);

            if (v.getWalletType() != null) {
                walletResponse.setWalletName(v.getWalletType().getDisplayName());
                walletResponse.setWalletRawName(v.getWalletType());
            }

            Coin coin = coinService.findByUnit(v.getCoinId());
            if (v.getCoinId() != null) {
                walletResponse.setCoinName(coin.getName());
                walletResponse.setCoinUnit(coin.getUnit());
                walletResponse.setCoinURL(coin.getIconUrl());
            }

            return walletResponse;
        });
    }

    /**
     * Transfer amount between admin wallets
     *
     * @param request Transfer request
     * @return Transfer result
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageResult transfer(AuthAdmin admin, AdminWalletTransferRequest request) {
        // Validation
        MessageResult validatedRequest = validateTransferRequest(request);
        if (validatedRequest.getCode() != 0) {
            return validatedRequest;
        }

        // Process transfer
        try {
            // Lock wallets to prevent concurrent modifications
            AdminWallet fromWallet = adminWalletDao.findByWalletTypeAndCoinIdWithLock(request.getFromWalletType(), request.getCoinId())
                    .orElseThrow(() -> new RuntimeException("From wallet not found"));
            AdminWallet toWallet = adminWalletDao.findByWalletTypeAndCoinIdWithLock(request.getToWalletType(), request.getCoinId())
                    .orElseThrow(() -> new RuntimeException("To wallet not found"));

            // Check balance
            if (fromWallet.getBalance().compareTo(request.getAmount()) < 0) {
                return MessageResult.error(messageSource.getMessage("INSUFFICIENT_BALANCE"));
            }

            // Perform transfer
            fromWallet.setBalance(fromWallet.getBalance().subtract(request.getAmount()));
            toWallet.setBalance(toWallet.getBalance().add(request.getAmount()));

            adminWalletDao.saveAll(List.of(fromWallet, toWallet));

            // Record transaction
            AdminTransaction transaction = new AdminTransaction();
            transaction.setTransactionNo(GeneratorUtil.getOrderId(AdminTransactionType.WALLET_TRANSFER.getName()));
            transaction.setFromWalletType(request.getFromWalletType());
            transaction.setToWalletType(request.getToWalletType());
            transaction.setCoinId(request.getCoinId());
            transaction.setAmount(request.getAmount());
            transaction.setBalance(fromWallet.getBalance());
            transaction.setTransactionType(AdminTransactionType.WALLET_TRANSFER);
            transaction.setAdminId(admin.getId());
            transaction.setDescription(request.getDescription());
            transaction.setStatus(AdminTransactionStatus.SUCCESS);

            adminTransactionDao.save(transaction);

            log.info("Admin wallet transfer completed: {} -> {}, amount: {}, admin: {}",
                    request.getFromWalletType(), request.getToWalletType(),
                    request.getAmount(), admin.getId());

            return MessageResult.success();

        } catch (Exception ex) {
            log.error("Admin wallet transfer failed", ex);
            throw new TransferException(messageSource.getMessage("TRANSFER_FAILED", new Object[]{ex.getMessage()}));
        }
    }

    /**
     * Get Wallet Transaction History
     *
     * @param screen filter and pagination
     * @return Transaction history with pagination
     */
    @Override
    public MessageResult transactionHistory(WalletTransactionHistoryRequest screen) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        int pageNo = screen.getPageNo();
        int pageSize = screen.getPageSize();
        Pageable pageable = PageRequest.of(pageNo > 0 ? pageNo - 1 : 0, pageSize);

        AdminWalletType walletName = screen.getWalletName();
        if(walletName != null) {
            booleanExpressions.add(QAdminTransaction.adminTransaction.fromWalletType.eq(walletName).or(QAdminTransaction.adminTransaction.toWalletType.eq(walletName)));
        }

        Integer transactionType = screen.getTransactionType();
        if(transactionType != null) {
           booleanExpressions.add(QAdminTransaction.adminTransaction.transactionType.eq(AdminTransactionType.getByOrdinal(transactionType)));
        }

        String tokenId = screen.getCoinId();
        if(!StringUtils.isBlank(tokenId)) {
            booleanExpressions.add(QAdminTransaction.adminTransaction.coinId.containsIgnoreCase(tokenId));
        }

        try {
            OffsetDateTime startTime = screen.getStartTime();
            if(startTime != null) {
                booleanExpressions.add(QAdminTransaction.adminTransaction.createTime.goe(startTime.toInstant()));
            }

            OffsetDateTime endTime = screen.getEndTime();
            if(endTime != null) {
                booleanExpressions.add(QAdminTransaction.adminTransaction.createTime.loe(endTime.toInstant()));
            }
        } catch (DateTimeException ex) {
            throw new IllegalArgumentException("Invalid date format. Please use ISO-8601 format like yyyy-MM-dd'T'HH:mm:ssXXX (e.g., " +
                    "2025-08-13T00:00:00+07:00)");
        }

        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);
        Page<AdminTransaction> allData = adminTransactionDao.findAll(predicate, pageable);
        Page<AdminWalletTransactionHistoryResponse> page = this.mapDataToDTO(allData);
        return MessageResult.success("Get list transaction history successfully", page);
    }

    /**
     * Check if admin has sufficient balance in PNL wallet
     *
     * @param user            Admin user
     * @param requiredBalance Required balance
     * @return Check result
     */
    @Override
    public MessageResult checkSufficientBalance(AuthMember user, BigDecimal requiredBalance) {

        Admin admin = adminService.findByUsername(user.getEmail());
        if (admin == null) {
            return MessageResult.error(messageSource.getMessage("USER_NOT_FOUND", new Object[]{user.getEmail()}));
        }

        if (requiredBalance == null || requiredBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Invalid symbol or required balance");
        }

        AdminWallet adminWallet = adminWalletDao.findByWalletTypeAndStatus(AdminWalletType.PNL, Constants.ENABLE_STATUS);
        if (!isSufficientPnlBalance(requiredBalance, adminWallet)) {
            return MessageResult.error(messageSource.getMessage("INSUFFICIENT_FUND",
                    new Object[]{requiredBalance}));
        }

        return MessageResult.success(messageSource.getMessage("SUFFICIENT_FUND",
                new Object[]{requiredBalance}));
    }

    public boolean isSufficientPnlBalance(BigDecimal requiredBalance, AdminWallet adminWallet) {

        if (requiredBalance == null || requiredBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Invalid required balance");
        }

        if (adminWallet == null) {
            throw new IllegalArgumentException("PNL Wallet not found");
        }

        return adminWallet.getBalance().compareTo(requiredBalance) >= 0;
    }

    @Override
    @Transactional
    public void calculateTotalPnlBalance(BigDecimal amount) {
        if (amount == null) {
            throw new IllegalArgumentException("Invalid amount");
        }

        AdminWallet adminWallet = adminWalletDao
                .findByWalletTypeAndStatusWithLock(AdminWalletType.PNL, Constants.ENABLE_STATUS);

        if (adminWallet == null) {
            throw new IllegalArgumentException("PNL Wallet not found");
        }

        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            // Negative PNL → add money to admin wallet
            adminWallet.setBalance(adminWallet.getBalance().add(amount.abs()));
        } else {
            // Positive PNL → deduct money from admin wallet
            if (adminWallet.getBalance().compareTo(amount) < 0) {
                throw new IllegalArgumentException("Insufficient balance in PNL wallet");
            }
            adminWallet.setBalance(adminWallet.getBalance().subtract(amount));
        }

        adminWalletDao.save(adminWallet);
    }


    private Page<AdminWalletTransactionHistoryResponse> mapDataToDTO(Page<AdminTransaction> allData) {
        return allData.map(v -> {
            AdminWalletTransactionHistoryResponse response = new AdminWalletTransactionHistoryResponse();
            BeanUtils.copyProperties(v, response);

            if(v.getFromWalletType() != null) {
                response.setFromWalletType(v.getFromWalletType().getDisplayName());
            }

            if(v.getToWalletType() != null) {
                response.setToWalletType(v.getToWalletType().getDisplayName());
            }

            if(v.getTransactionType() != null) {
                response.setTransactionType(v.getTransactionType().getDisplayName());
            }

            Coin coin = coinService.findByUnit(v.getCoinId());
            if(coin != null) {
                response.setCoinUnit(coin.getUnit());
                response.setCoinName(coin.getName());
                response.setCoinURL(coin.getIconUrl());
            }

            Coinext coinext = coinextService.findFirstByCoinnameAndProtocol(v.getCoinId(), v.getNetwork());
            if(coinext != null) {
                 response.setNetwork(coinext.getProtocolname());
            }
            return response;
        });
    }

    /**
     * Validate transfer request
     *
     * @param request Transfer request
     * @return Validation result
     */
    private MessageResult validateTransferRequest(AdminWalletTransferRequest request) {
        try {
            // Validate wallets are not the same
            if (request.getFromWalletType().equals(request.getToWalletType())) {
                return MessageResult.error(messageSource.getMessage("CANNOT_TRANSFER_TO_SAME_WALLET"));
            }
            // Master wallet validation - at least one wallet must be master
            boolean fromIsMaster = request.getFromWalletType().equals(AdminWalletType.MASTER);
            boolean toIsMaster = request.getToWalletType().equals(AdminWalletType.MASTER);
            if (!fromIsMaster && !toIsMaster) {
                return MessageResult.error(messageSource.getMessage("AT_LEAST_ONE_WALLET_MUST_BE_MASTER"));
            }

            // Check amount
            if (request.getAmount() == null || BigDecimal.ZERO.compareTo(request.getAmount()) >= 0) {
                return MessageResult.error(messageSource.getMessage("INVALID_TRANSFER_AMOUNT"));
            } else if (fiatCoins.contains(request.getCoinId()) && request.getAmount().scale() > 2) {
                return MessageResult.error(messageSource.getMessage("INVALID_TRANSFER_FIAT_COIN_AMOUNT"));
            } else if (request.getAmount().scale() > 8) {
                return MessageResult.error(messageSource.getMessage("INVALID_TRANSFER_CRYPTO_COIN_AMOUNT"));
            } else if (maxTransferAmount != null && request.getAmount().compareTo(maxTransferAmount) > 0) {
                return MessageResult.error(messageSource.getMessage("AMOUNT_EXCEEDS_THE_LIMITATION"));
            }

            return MessageResult.success();
        } catch (Exception ex) {
            log.error("Validate transfer request failed", ex);
            return MessageResult.error(messageSource.getMessage("VALIDATE_TRANSFER_REQUEST_FAILED"));
        }
    }
}

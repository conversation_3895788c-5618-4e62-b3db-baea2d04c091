package com.icetea.lotus.service.admin.impl;

import com.icetea.lotus.constant.AdminStatus;
import com.icetea.lotus.constant.FirstTimeLoginStatus;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.dao.AdminDao;
import com.icetea.lotus.dto.KeycloakUserDTO;
import com.icetea.lotus.dto.request.EditUserRequest;
import com.icetea.lotus.dto.request.GenerateAccountRequest;
import com.icetea.lotus.dto.request.RegisterRequest;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Department;
import com.icetea.lotus.entity.spot.SysRole;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.job.PasswordGenerator;
import com.icetea.lotus.service.*;
import com.icetea.lotus.service.admin.AdminManagementService;
import com.icetea.lotus.service.notification.EmailService;
import com.icetea.lotus.util.MessageResult;
import freemarker.template.TemplateException;
import jakarta.mail.MessagingException;
import jakarta.persistence.PersistenceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminManagementServiceImpl extends BaseController implements AdminManagementService {

    private final AdminService adminService;

    private final AdminDao adminDao;

    private final MemberService memberService;

    private final EmailService emailService;

    private final SysRoleService sysRoleService;

    private final PasswordEncoder passwordEncoder;

    private final KeycloakService keycloakService;

    private final LocaleMessageSourceService messageSource;

    private static final String EMAIL_REGEX = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";

    private static final String NAME_REGEX = "^[A-Za-z ]+$";

    private static final String PHONE_REGEX = "^\\d+$";

    /**
     * Validate username and process soft deleted user admin
     *
     * @param userId admin's userId
     * @return MessageResult
     */
    @Override
    public MessageResult terminateAdmin(Long userId, AuthAdmin authAdmin) {
        Admin terminatedAdmin = adminService.findById(userId);
        if (terminatedAdmin == null) {
            return MessageResult.error("User admin not found");
        }
        if(AdminStatus.TERMINATED.equals(terminatedAdmin.getStatus())) {
            return MessageResult.error("User admin already been terminated");
        }

        MessageResult message = this.checkSelfDelete(userId, authAdmin);
        if (message.getCode() != 0) {
            return message;
        }

        terminatedAdmin.setStatus(AdminStatus.TERMINATED);
        terminatedAdmin.setTerminatedTime(new Date());
        try {
            adminDao.save(terminatedAdmin);
        } catch (PersistenceException e) {
            return MessageResult.error("Failed to terminate account. Please try again.");
        }
        return MessageResult.success("User account has been terminated successfully");
    }

    /**
     * Add new user
     *
     * @param registerRequest data request
     * @return message
     */
    @Override
    public MessageResult addNewMember(RegisterRequest registerRequest) {
        String email = registerRequest.getEmail();
        String phone = registerRequest.getPhoneNumber();
        String fullName = registerRequest.getFullName();
        //validate data
        MessageResult resultRequest = checkDataRequest(email, phone, fullName);
        if (resultRequest.getCode() != 0) {
            return resultRequest;
        }

        // check duplicate
        List<Admin> listAdmin = adminDao.findAll();
        MessageResult message = checkDuplicateEmailAndPhone(listAdmin, email, phone);
        if (message.getCode() != 0) {
            return message;
        }

        // generates username and password
        GenerateAccountRequest accountRequest = generateAccountForEmail(email);

        // save into db
        Long roleId = registerRequest.getRoleId();
        MessageResult saveAdminWithEveryRole = saveAdminWithEveryRole(roleId, accountRequest, email, phone, fullName);
        if (saveAdminWithEveryRole.getCode() != 0) {
            return saveAdminWithEveryRole;
        }
        // send to email registered
        try {
            emailService.sendEmailMsgAddNewUser(accountRequest.getUsername(), accountRequest.getPassword(), email);
        } catch (MessagingException | IOException | TemplateException e) {
            log.info(e.getMessage());
        }
        return success();
    }

    @Override
    public MessageResult lockUser(Long id, boolean lock, AuthAdmin currentUser) {

        Admin user = adminService.findById(id);

        if (user == null) {
            return MessageResult.error(messageSource.getMessage("USER_NOT_FOUND", new Object[]{id}));
        }

        if (AdminStatus.TERMINATED.equals(user.getStatus())) {
            return MessageResult.error(messageSource.getMessage("USER_ALREADY_TERMINATED"));
        }

        if (currentUser.getId() == user.getId()) {
            return MessageResult.error(messageSource.getMessage("CANNOT_SELF_LOCK"));
        }

        String successMessage = messageSource.getMessage("LOCK_USER_SUCCESS");
        if (lock) {
            user.setStatus(AdminStatus.LOCK);
            log.info("User {} with accountId {} has been LOCKED at {}",
                    user.getUsername(), user.getId(), LocalDateTime.now());
        }else {
            user.setStatus(AdminStatus.ACTIVE);
            log.info("User {} with accountId {} has been UNLOCKED at {}",
                    user.getUsername(), user.getId(), LocalDateTime.now());
            successMessage = messageSource.getMessage("UNLOCK_USER_SUCCESS");
        }

        adminService.save(user);

        try {
            emailService.sendLockUserStatusEmail(user.getUsername(), user.getId().toString(), user.getEmail(), lock);
        }  catch (MessagingException | IOException | TemplateException e) {
            log.info(e.getMessage());
        }

        return MessageResult.success(successMessage);
    }

    /**
     * Updates an existing user's information, including email, phone, name, and role.
     * <p>
     * Validates the request, checks for duplicate email/phone, updates the user in the database,
     * and synchronizes changes with Keycloak. Returns errors if validation fails or Keycloak update fails.
     * </p>
     *
     * @param editRequest user data to update
     * @return {@link MessageResult} indicating success or specific validation/error messages
     */
    @Override
    public MessageResult editUser(EditUserRequest editRequest) {
        // validate request
        Admin user = adminService.findById(editRequest.getUserId());
        if (user == null) {
            return MessageResult.error(messageSource.getMessage("USER_NOT_FOUND", new Object[]{editRequest.getUserId()}));
        }

        if (adminService.isEmailInUseByOtherUser(editRequest.getEmail(), editRequest.getUserId())){
            return MessageResult.error(messageSource.getMessage("EMAIL_ALREADY_IN_USE"));
        }

        if (adminService.isPhoneInUseByOtherUser(editRequest.getPhoneNumber(), editRequest.getUserId())){
            return MessageResult.error(messageSource.getMessage("PHONE_ALREADY_IN_USE"));
        }

        String oldUsername = user.getUsername();

        user.setUsername(editRequest.getEmail());
        user.setEmail(editRequest.getEmail());
        user.setMobilePhone(editRequest.getPhoneNumber());
        user.setRealName(editRequest.getFullName());

        long roleId = editRequest.getRoleId();
        SysRole roleEntity = sysRoleService.findById(roleId);
        if(roleEntity == null){
            return MessageResult.error(messageSource.getMessage("ROLE_NOT_FOUND", new Object[]{roleId}));
        }
        user.setRoleId(roleId);

        // update keycloak
        try {
            KeycloakUserDTO keycloakAdmin = new KeycloakUserDTO();
            keycloakAdmin.setUsername(user.getUsername());
            keycloakAdmin.setEmail(user.getEmail());
            keycloakAdmin.setMobilePhone(user.getMobilePhone());
            keycloakAdmin.setFirstName(user.getRealName());
            keycloakAdmin.setLastName(user.getRealName());
            keycloakService.updateKeycloakUserByUsername(oldUsername, keycloakAdmin);
            // consider assign role for user when permission done
        } catch (Exception e) {
            return error(e.getMessage());
        }

        adminService.saveAdmin(user);
        return success(messageSource.getMessage("EDIT_USER_SUCCESS"));
    }


    private MessageResult checkSelfDelete(Long userId, AuthAdmin authAdmin) {
        if(authAdmin != null) {
            boolean checkSelfDelete = userId.equals(authAdmin.getId());
            if(checkSelfDelete) {
                return  error("Can not self delete current account, please use another one");
            }
        }
        return success();
    }

    /**
     * Generate account to send to email
     *
     * @param email destination email
     * @return data
     */
    private GenerateAccountRequest generateAccountForEmail(String email) {
        GenerateAccountRequest accountRequest = new GenerateAccountRequest();
        PasswordGenerator simplePasswordGenerator = new PasswordGenerator();
        String generatePassword = simplePasswordGenerator.generatePassword();
        accountRequest.setUsername(email);
        accountRequest.setPassword(generatePassword);
        return accountRequest;
    }

    /**
     * Save into db with each role
     *
     * @param roleId         role id
     * @param accountRequest generate account data
     * @param email          email
     * @param phone          phone number
     * @param fullName       full name
     * @return message
     */
    private MessageResult saveAdminWithEveryRole(Long roleId, GenerateAccountRequest accountRequest, String email,
                                                 String phone, String fullName) {
        // find by id
        Optional<SysRole> sysRole = sysRoleService.findRoleById(roleId);
        if (sysRole.isEmpty()) {
            return error("Role not found");
        }
        String passwordEncode = passwordEncoder.encode(accountRequest.getPassword());
        String username = accountRequest.getUsername();
        Department department = new Department();
        department.setId(1L);

        //save admin
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setPassword(passwordEncode);
        admin.setEmail(email);
        admin.setMobilePhone(phone);
        admin.setRealName(fullName);
        admin.setDepartment(department);
        admin.setRoleId(roleId);
        admin.setFirstTimeLoginStatus(FirstTimeLoginStatus.NEW);

        //save to keycloak
        try {
            KeycloakUserDTO keycloakAdmin = new KeycloakUserDTO();
            keycloakAdmin.setUsername(admin.getUsername());
            keycloakAdmin.setEmail(admin.getEmail());
            if (admin.getRealName() != null) {
                keycloakAdmin.setFirstName(admin.getRealName());
                keycloakAdmin.setLastName(admin.getRealName());
            }
            keycloakAdmin.setPassword(accountRequest.getPassword());
            keycloakAdmin.setMobilePhone(admin.getMobilePhone());
            String keycloakUserId = keycloakService.createKeycloakUserAndGetId(keycloakAdmin);
            keycloakService.assignRoleToUser(keycloakUserId, "admin");
        } catch (Exception e) {
            return error("Failed to create user in Keycloak");
        }
        adminService.saveAdmin(admin);
        return success();
    }

    /**
     * Check exist email and phone
     *
     * @param admins list admin
     * @param email  email
     * @param phone  phone number
     * @return message
     */
    private MessageResult checkDuplicateEmailAndPhone(List<Admin> admins, String email, String phone) {
        boolean checkExistEmail = admins.stream().
                anyMatch(e -> email.equalsIgnoreCase(e.getEmail()));
        boolean checkExistPhone = admins.stream().
                anyMatch(p -> phone.equalsIgnoreCase(p.getMobilePhone()));
        if (checkExistEmail) {
            return error("Email already exists");
        }
        if (checkExistPhone) {
            return error("Phone number already exists");
        }
        return success();
    }

    public MessageResult checkDataRequest(String email, String phone, String fullName) {
        //check full name
        if (!(isValidFullName(fullName))) {
            return error("Full name is invalid format or missing");
        }


        // check email
        if (!(isValidEmail(email))) {
            return error("Email is invalid format or missing");
        }

        // check phone
        if (!(isValidPhone(phone))) {
            return error("Phone number is invalid format or missing");
        }
        return success();
    }

    private boolean isValidFullName(String fullName) {
        return fullName != null
                && !fullName.isBlank()
                && fullName.length() <= 100
                && fullName.matches(NAME_REGEX);
    }

    private boolean isValidEmail(String email) {
        return email != null
                && !email.isBlank()
                && email.matches(EMAIL_REGEX);
    }

    private boolean isValidPhone(String phone) {
        return phone != null
                && !phone.isBlank()
                && phone.length() >= 8
                && phone.length() <= 15
                && phone.matches(PHONE_REGEX);
    }
}

package com.icetea.lotus.service.admin;

import com.icetea.lotus.dto.request.EditUserRequest;
import com.icetea.lotus.dto.request.RegisterRequest;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.util.MessageResult;


public interface AdminManagementService {
    MessageResult terminateAdmin(Long userId, AuthAdmin user);

    MessageResult addNewMember(RegisterRequest registerRequest);

    MessageResult lockUser(Long id, boolean lock, AuthAdmin currentUser);

    MessageResult editUser(EditUserRequest editRequest);
}

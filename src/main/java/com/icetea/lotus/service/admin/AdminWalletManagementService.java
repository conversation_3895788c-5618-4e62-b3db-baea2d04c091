package com.icetea.lotus.service.admin;

import com.icetea.lotus.dto.request.AdminWalletListRequest;
import com.icetea.lotus.dto.request.AdminWalletTransferRequest;
import com.icetea.lotus.dto.request.WalletTransactionHistoryRequest;
import com.icetea.lotus.dto.response.ListingAdminWalletResponse;
import com.icetea.lotus.entity.spot.AdminWallet;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.util.MessageResult;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;

public interface AdminWalletManagementService {
    Page<ListingAdminWalletResponse> listingWallet(AdminWalletListRequest screen);

    MessageResult transfer(AuthAdmin admin, AdminWalletTransferRequest request);

    MessageResult transactionHistory(WalletTransactionHistoryRequest screen);

    MessageResult checkSufficientBalance(AuthMember user, BigDecimal requiredBalance);

    boolean isSufficientPnlBalance(BigDecimal requiredBalance, AdminWallet adminWallet);

    void calculateTotalPnlBalance(BigDecimal amount);
}

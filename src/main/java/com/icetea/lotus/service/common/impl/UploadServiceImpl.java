package com.icetea.lotus.service.common.impl;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.icetea.lotus.config.MinioConfig;
import com.icetea.lotus.config.S3Config;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.UploadService;
import com.icetea.lotus.util.GeneratorUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.UploadFileUtil;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.SetBucketPolicyArgs;
import io.minio.errors.MinioException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.InternalServerErrorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

/**
 * The type Upload service.
 */
@Slf4j
@Service
public class UploadServiceImpl extends BaseController implements UploadService {
    private final MinioClient minioClient;
    private final MinioConfig minioConfig;
    private final LocaleMessageSourceService sourceService;

    private static final String ALLOWED_FORMAT = ".jpg,.gif,.png";
    private static final String UTF8 = "UTF-8";
    private static final String FORMAT_NOT_SUPPORTED = "FORMAT_NOT_SUPPORTED";
    private static final String FILE_NOT_FOUND = "FILE_NOT_FOUND";
    private static final String UPLOAD_SUCCESS = "UPLOAD_SUCCESS";
    private static final String FAILED_TO_WRITE = "FAILED_TO_WRITE";
    private static final String REQUEST_FAILED = "REQUEST_FAILED";

    private final S3Config s3Config;

    public UploadServiceImpl(
            S3Config s3Config,
            MinioClient minioClient,
            MinioConfig minioConfig,
            LocaleMessageSourceService sourceService) {
        super();
        this.s3Config = s3Config;
        this.minioClient = minioClient;
        this.minioConfig = minioConfig;
        this.sourceService = sourceService;
    }

    @Value("${oss.name}")
    private String ossName;

    @Override
    public MessageResult uploadOssImage(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException {
        log.info(String.valueOf(request.getSession().getServletContext().getResource("/")));
        response.setCharacterEncoding(UTF8);
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            return MessageResult.error(500, sourceService.getMessage(FORMAT_NOT_SUPPORTED));
        }
        if (file == null) {
            return MessageResult.error(500, sourceService.getMessage(FILE_NOT_FOUND));
        }

        String directory = new SimpleDateFormat("yyyy/MM/dd/").format(new Date());
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf("."), fileName.length());
        String key = directory + GeneratorUtil.getUUID() + suffix;

        return doUpload(file, key);
    }

    @Override
    public MessageResult uploadLocalImage(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException {
        log.info(request.getSession().getServletContext().getResource("/").toString());
        response.setCharacterEncoding(UTF8);
        response.setContentType("text/html; charset=UTF-8");
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            return MessageResult.error(500, sourceService.getMessage(FORMAT_NOT_SUPPORTED));
        }
        Assert.isTrue(file != null, sourceService.getMessage(FILE_NOT_FOUND));
        // Verify file type
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf("."), fileName.length());
        if (!ALLOWED_FORMAT.contains(suffix.trim().toLowerCase())) {
            return MessageResult.error(sourceService.getMessage(FORMAT_NOT_SUPPORTED));
        }
        String result = UploadFileUtil.uploadFile(file, fileName);
        if (result != null) {
            MessageResult mr = new MessageResult(0, sourceService.getMessage(UPLOAD_SUCCESS));
            mr.setData(result);
            return mr;
        } else {
            MessageResult mr = new MessageResult(0, sourceService.getMessage(FAILED_TO_WRITE));
            mr.setData(result);
            return mr;
        }
    }

    @Override
    public MessageResult uploadOssApp(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException {
        log.info(String.valueOf(request.getSession().getServletContext().getResource("/")));
        response.setCharacterEncoding(UTF8);
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            return MessageResult.error(500, sourceService.getMessage(FORMAT_NOT_SUPPORTED));
        }
        if (file == null) {
            return MessageResult.error(500, sourceService.getMessage(FILE_NOT_FOUND));
        }

        String directory = "appdownload/";
        String fileName = file.getOriginalFilename();
        String key = directory + fileName;

        return doUpload(file, key);
    }

    @Override
    public MessageResult base64UpLoad(String base64Data) {
        MessageResult result = new MessageResult();
        try {
            log.info("Upload file data:{}", base64Data);
            String dataPrix = "";
            String data = "";

            log.debug("Make judgments on data");
            if (base64Data == null || base64Data.isEmpty()) {
                throw new InternalServerErrorException("Upload failed, upload image data is empty");
            } else {
                String[] d = base64Data.split("base64,");
                if (d.length == 2) {
                    dataPrix = d[0];
                    data = d[1];
                } else {
                    throw new InternalServerErrorException("Upload failed, data is illegal");
                }
            }

            log.debug("Parse the data and get file name and stream data");
            String suffix;
            if ("data:image/jpeg;".equalsIgnoreCase(dataPrix)) {// data:image/jpeg; base64, base64 encoded jpeg image data
                suffix = ".jpg";
            } else if ("data:image/x-icon;".equalsIgnoreCase(dataPrix)) {// data:image/x-icon; base64, base64 encoded icon image data
                suffix = ".ico";
            } else if ("data:image/gif;".equalsIgnoreCase(dataPrix)) {// data:image/gif; base64, base64 encoded gif image data
                suffix = ".gif";
            } else if ("data:image/png;".equalsIgnoreCase(dataPrix)) {// data:image/png; base64, base64 encoded png image data
                suffix = ".png";
            } else {
                throw new IllegalArgumentException("The uploaded image format is illegal");
            }
            String directory = new SimpleDateFormat("yyyy/MM/dd/").format(new Date());
            String key = directory + GeneratorUtil.getUUID() + suffix;

            // Because of the jar problem of BASE64Decoder, here is the toolkit provided by the spring framework.
            byte[] bs = Base64.getDecoder().decode(data);
            return doUpload(key, bs);
        } catch (Exception e) {
            log.warn("Upload failed,{}", e.getMessage());
            result.setCode(500);
            result.setMessage("Upload failed," + e.getMessage());
        }
        return result;
    }


    private MessageResult doUpload(MultipartFile file, String key) {
        return minioUpload(file, key);
    }

    private MessageResult doUpload(String key, byte[] bs) {
        //Only support s3Update
        return s3Upload(key, bs);
    }


    private MessageResult s3Upload(String key, byte[] bs) {
        String[] split = s3Config.getRegionsName().split("-");
        StringBuilder regionName = new StringBuilder();
        for (String s : split) {
            regionName.append(s.toUpperCase()).append("_");
        }
        regionName = new StringBuilder(regionName.substring(0, regionName.length() - 1));
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(s3Config.getAccessKeyId(), s3Config.getAccessKeySecret());
        AmazonS3 s3 = AmazonS3ClientBuilder.standard().withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                .withRegion(Regions.valueOf(regionName.toString())).build();

        PutObjectRequest putRequest = null;
        try {
            InputStream is = new ByteArrayInputStream(bs);
            putRequest = new PutObjectRequest(s3Config.getBucketName(), key, is, new ObjectMetadata());
            s3.putObject(putRequest);
            String uri = s3Config.toUrl(key);
            MessageResult mr = new MessageResult(0, sourceService.getMessage(UPLOAD_SUCCESS));
            mr.setData(uri);
            log.info("Uploaded successfully...");
            return mr;
        } catch (Exception e) {
            return MessageResult.error(500, sourceService.getMessage(REQUEST_FAILED));
        }

    }

    private MessageResult minioUpload(MultipartFile file, String key) {
        log.debug("Uploading file to MinIO: bucket={}, key={}", minioConfig.getBucketName(), key);

        if (file.isEmpty()) {
            log.warn("Upload failed: Empty file");
            return MessageResult.error(400, sourceService.getMessage("FILE_EMPTY"));
        }

        try {
            // Prepare upload arguments
            InputStream inputStream = file.getInputStream();
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(key)
                    .stream(inputStream, file.getSize(), -1) // -1 indicates unknown size
                    .contentType(file.getContentType() != null ? file.getContentType() : "application/octet-stream")
                    .build();

            // Perform upload
            minioClient.putObject(putObjectArgs);

            // Create public URL for fe access
            String fileUrl = generateMinioUrl(minioConfig.getUrl(), minioConfig.getBucketName(), key);

            log.info("File uploaded successfully: {}", fileUrl);

            MessageResult result = new MessageResult(0, sourceService.getMessage(UPLOAD_SUCCESS));
            result.setData(fileUrl);
            return result;

        } catch (MinioException e) {
            log.error("MinIO error during upload: {}", e.getMessage());
            return MessageResult.error(500, sourceService.getMessage("MINIO_UPLOAD_FAILED") + ": " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during upload: {}", e.getMessage());
            return MessageResult.error(500, sourceService.getMessage(REQUEST_FAILED));
        }
    }

    private String generateMinioUrl(String endpoint, String bucketName, String key) {
        String baseUrl = endpoint.endsWith("/") ? endpoint.substring(0, endpoint.length() - 1) : endpoint;
        return String.format("%s/api/v1/buckets/%s/objects/download?preview=true&prefix=%s", baseUrl, bucketName, key);
    }

    /**
     * Makes a MinIO bucket publicly accessible by setting a bucket policy that allows
     * anonymous read access to all objects within the bucket.
     *
     * @param bucketName the name of the bucket to make public
     */
    private void makeBucketPublic(String bucketName) { // NOSONAR
        try {
            String policyJson = """
                    {
                        "Version": "2025-08-15",
                        "Statement": [
                            {
                                "Effect": "Allow",
                                "Principal": "*",
                                "Action": [
                                    "s3:GetObject"
                                ],
                                "Resource": [
                                    "arn:aws:s3:::%s/*"
                                ]
                            }
                        ]
                    }
                    """.formatted(bucketName);

            minioClient.setBucketPolicy(
                    SetBucketPolicyArgs.builder()
                            .bucket(bucketName)
                            .config(policyJson)
                            .build()
            );

            log.info("Bucket {} is now public!", bucketName);
        } catch (Exception e) {
            log.error("Error making bucket public: {}", e.getMessage());
        }
    }

}

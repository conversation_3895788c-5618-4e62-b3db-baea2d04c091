package com.icetea.lotus.service.common.impl;

import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * The type Base admin service.
 */
@Service
@RequiredArgsConstructor
public class BaseAdminServiceImpl extends BaseController implements BaseAdminService {

    private final LocaleMessageSourceService msService;

    @Override
    public Admin getAdmin(HttpServletRequest request) {
        HttpSession session = request.getSession();
        return (Admin) session.getAttribute(SysConstant.SESSION_ADMIN);
    }

    @Override
    public MessageResult checkCode(String code, String key) {
        return success(msService.getMessage("CODE_CORRECT"));
    }
}

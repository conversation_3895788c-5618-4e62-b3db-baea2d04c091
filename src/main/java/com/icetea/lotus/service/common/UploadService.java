package com.icetea.lotus.service.common;

import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface UploadService {
    MessageResult uploadOssImage(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException;

    MessageResult uploadLocalImage(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException;

    MessageResult uploadOssApp(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException;

    MessageResult base64UpLoad(String base64Data);
}

package com.icetea.lotus.controller.finance;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedDepositService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * deposit management
 */
@Slf4j
@RestController
@RequestMapping("/management/deposit")
public class DepositController extends BaseAdminController {

    private final ExtendedDepositService extendedDepositService;

    public DepositController(BaseAdminService baseAdminService, ExtendedDepositService extendedDepositService) {
        super(baseAdminService);
        this.extendedDepositService = extendedDepositService;
    }

    /**
     * Retrieves the list of currencies available in the deposit records.
     *
     * @return MessageResult containing the list of available currencies for deposit.
     */
    @GetMapping("/coin-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency list in the deposit record")
    public MessageResult coinList() {
        return extendedDepositService.coinList();

    }

    /**
     * Retrieves the list of currency protocols associated with deposit records.
     *
     * @return MessageResult containing the list of currency protocols for deposit.
     */
    @GetMapping("/protocol-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency protocol list in the deposit record")
    public MessageResult protocolList() {
        return extendedDepositService.protocolList();

    }

    /**
     * Retrieves a list of deposit transaction's status
     *
     * @return MessageResult contains list of deposit transaction's status
     */
    @GetMapping("/transaction-statuses")
    public MessageResult getListStatus() {
        return extendedDepositService.getStatusList();
    }
}


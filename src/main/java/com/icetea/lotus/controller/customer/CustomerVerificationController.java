package com.icetea.lotus.controller.customer;

import com.icetea.lotus.constant.AuditStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.CustomerVerificationRequest;
import com.icetea.lotus.dto.request.StatusUpdateRequest;
import com.icetea.lotus.service.customer.CustomerVerificationService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/customer/verification")
@RequiredArgsConstructor
public class CustomerVerificationController {

    private final CustomerVerificationService customerVerificationService;

    /**
     * Retrieves a paginated list of customer verification records based on the provided criteria.
     *
     * @param pageModel the pagination parameters including page number and size
     * @param request   the filter criteria for customer verification search
     * @return MessageResult containing the paginated customer verification data
     */
    @PreAuthorize("hasRole('customer-management:view-kyc')")
    @GetMapping("/page-query")
    public MessageResult queryPage(PageModel pageModel,
                                   CustomerVerificationRequest request) {
        return customerVerificationService.queryPage(pageModel, request);
    }

    /**
     * Retrieves detailed information for a specific customer verification record.
     *
     * @param id the unique identifier of the customer verification record
     * @return MessageResult containing the customer verification details
     */
    @PreAuthorize("hasRole('customer-management:view-kyc-detail')")
    @GetMapping("/detail/{id}")
    public MessageResult detail(@PathVariable("id") Long id) {
        return customerVerificationService.detail(id);
    }

    /**
     * Approves a customer verification record.
     *
     * @param id the unique identifier of the customer verification record
     * @return MessageResult indicating the success or failure of the update operation
     */
    @PreAuthorize("hasRole('customer-management:update-kyc:approve')")
    @PatchMapping("/approve/{id}")
    public MessageResult approve(@PathVariable("id") Long id) {
        StatusUpdateRequest request = new StatusUpdateRequest();
        request.setStatus(AuditStatus.AUDIT_SUCCESS.getOrdinal());
        return customerVerificationService.updateStatus(id, request);
    }

    /**
     * Rejects a customer verification record.
     *
     * @param id the unique identifier of the customer verification record
     * @return MessageResult indicating the success or failure of the update operation
     */
    @PreAuthorize("hasRole('customer-management:update-kyc:reject')")
    @PatchMapping("/reject/{id}")
    public MessageResult reject(@PathVariable("id") Long id) {
        StatusUpdateRequest request = new StatusUpdateRequest();
        request.setStatus(AuditStatus.AUDIT_DEFEATED.getOrdinal());
        return customerVerificationService.updateStatus(id, request);
    }


}

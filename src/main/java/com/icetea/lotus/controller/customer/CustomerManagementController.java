package com.icetea.lotus.controller.customer;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.CustomerListRequest;
import com.icetea.lotus.service.customer.CustomerManagementService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Customer management controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/management/customer")
@RequiredArgsConstructor
public class CustomerManagementController {
    private final CustomerManagementService customerManagementService;

    /**
     * Retrieves a paginated list of customers based on the provided search criteria.
     *
     * @param pageModel the pagination model containing page size and number
     * @param request   the customer search criteria including filters for username, email, status, etc.
     * @return MessageResult containing the paginated customer list
     */
    @PreAuthorize("hasRole('customer-management:view')")
    @GetMapping("/page-query")
    public MessageResult pageQuery(
            PageModel pageModel,
            CustomerListRequest request,
            HttpServletRequest httpServletRequest) {
        return customerManagementService.pageQuery(pageModel, request, httpServletRequest);

    }

    /**
     * Update user status
     *
     * @return message
     */
    @PreAuthorize("hasRole('customer-management:update-trading')")
    @PutMapping("/{id}/update-trading-status")
    public MessageResult updateTradingStatus(@PathVariable Long id, Boolean spotTradingStatus, Boolean futureTradingStatus) {
        return customerManagementService.updateTradingStatus(id, spotTradingStatus, futureTradingStatus);
    }

    /**
     * Block/unblock customer
     * @param id the ID of the customer to block
     * @return message
     */
    @PreAuthorize("hasRole('customer-management:block-customer')")
    @PostMapping("/{id}/blocking")
    public MessageResult blockCustomer(@PathVariable Long id, @RequestParam boolean block) {
        return block ? customerManagementService.blockCustomer(id) : customerManagementService.unblockCustomer(id);
    }

    /**
     * Delete a customer by their ID.
     *
     * @param memberId the ID of the customer to delete
     * @return a MessageResult indicating the result of the operation
     */
    @PreAuthorize("hasRole('customer-management:delete-customer')")
    @PostMapping("/delete")
    public MessageResult deleteCustomer(@RequestParam Long memberId) {
        return customerManagementService.deleteCustomer(memberId);
    }
}

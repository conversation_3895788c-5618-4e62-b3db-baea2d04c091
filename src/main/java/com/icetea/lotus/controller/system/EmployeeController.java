package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.LoginRequest;
import com.icetea.lotus.dto.request.UpdatePasswordRequest;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.service.system.EmployeeService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;
import org.springframework.web.client.RestTemplate;

/**
 * The type Employee controller.
 */
@Slf4j
@RestController
@RequestMapping("/system/employee")
@RequiredArgsConstructor
public class EmployeeController {
    private final EmployeeService employeeService;

    /**
     * Admin login message result.
     * LoginRequest request body
     *
     * @return the message result
     */
    @PostMapping(value = "/login")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Determine the background login and enter the mobile phone verification code")
    public MessageResult adminLogin(@RequestBody @Valid LoginRequest loginRequest) {

        return employeeService.adminLogin(loginRequest);
    }

    /**
     * Submit login information
     *
     * @param username   the username
     * @param password   the password
     * @param phone      the phone
     * @param code       the code
     * @param rememberMe the remember me
     * @param request    the request
     * @return message result
     */
    @GetMapping(value = "sign/in")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Submit the login information to the Admin")
    public MessageResult doLogin(@SessionAttribute("username") String username,
                                 @SessionAttribute("password") String password,
                                 @SessionAttribute("phone") String phone, String code,
                                 @RequestParam(value = "rememberMe", defaultValue = "true") boolean rememberMe,
                                 HttpServletRequest request) {

        return employeeService.doLogin(username, password, phone, code, rememberMe, request);
    }

    /**
     * Valiate phone code message result.
     *
     * @param request the request
     * @return the message result
     */
    @GetMapping(value = "/check")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Determine the background login and enter the mobile phone verification code")
    public MessageResult valiatePhoneCode(HttpServletRequest request) {

        return employeeService.valiatePhoneCode(request);
    }


    /**
     * Log out
     *
     * @return message result
     */
    @GetMapping(value = "logout")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Log out")
    public MessageResult logout(RestTemplate restTemplate,
                                @RequestParam("refresh_token") String refreshToken
    ) {
        return employeeService.logout(restTemplate, refreshToken);
    }

    /**
     * Create or change backend users
     *
     * @param admin         the admin
     * @param departmentId  the department id
     * @param bindingResult the binding result
     * @return message result
     */
    @PostMapping(value = "/merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create or change background users")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult addAdmin(Admin admin,
                                  @RequestParam("departmentId") Long departmentId,
                                  BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return employeeService.addAdmin(admin, departmentId);
    }

    /**
     * Find all admin user message result.
     *
     * @param pageModel the page model
     * @param searchKey the search key
     * @return the message result
     */
    @GetMapping("page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Paginate to find the admin user in the background")
    public MessageResult findAllAdminUser(
            PageModel pageModel,
            @RequestParam(value = "searchKey", defaultValue = "") String searchKey) {

        return employeeService.findAllAdminUser(pageModel, searchKey);

    }

    /**
     * Update password
     *
     * @param updatePasswordRequest data request
     * @return message
     */
    @PutMapping("update-password")
    public MessageResult updatePassword(@RequestBody UpdatePasswordRequest updatePasswordRequest) {

        return employeeService.updatePassword(updatePasswordRequest);

    }

    /**
     * Reset password message result.
     *
     * @param id the id
     * @return the message result
     */
    @PostMapping("reset-password")
    public MessageResult resetPassword(Long id) {

        return employeeService.resetPassword(id);
    }

    /**
     * Admin Information
     *
     * @param user current user
     * @return message result
     */
    @GetMapping(value = "/my-info")
    public MessageResult adminDetail(@CurrentUser AuthAdmin user) {
        return employeeService.myInfo(user);

    }

    /**
     * Admin Information
     *
     * @param ids the ids
     * @return message result
     */
    @PreAuthorize("hasRole('system:employee:deletes')")
    @DeleteMapping(value = "/deletes")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Details of the Admin user in the background")
    public MessageResult deletes(Long[] ids) {
        return employeeService.deletes(ids);
    }


    @GetMapping(value = "/refresh-token")
    public MessageResult refreshToken(@RequestParam("refresh_token") String refreshToken) {
        log.info("RefreshToken==> refreshToken = {}", refreshToken);
        return employeeService.refreshToken(refreshToken);
    }
}

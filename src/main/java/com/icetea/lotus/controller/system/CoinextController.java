package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.Coinext;
import com.icetea.lotus.model.screen.CoinextScreen;
import com.icetea.lotus.service.system.ExtendedCoinextService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Currency expansion management
 */
@Slf4j
@RestController
@RequestMapping("/system/coinext")
@RequiredArgsConstructor
public class CoinextController {

    private final ExtendedCoinextService extendedCoinextService;

    /**
     * Coin list message result.
     *
     * @return the message result
     */
    @GetMapping("/coin-list")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Get the currency list in currency expansion")
    public MessageResult coinList() {
        return extendedCoinextService.coinList();
    }

    /**
     * Protocol list message result.
     *
     * @return the message result
     */
    @GetMapping("/protocol-list")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Get the currency protocol list in currency expansion")
    public MessageResult protocolList() {
        return extendedCoinextService.protocolList();
    }

    /**
     * Page query message result.
     *
     * @param pageModel     the page model
     * @param coinextScreen the coinext screen
     * @return the message result
     */
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Get the currency expansion list")
    public MessageResult pageQuery(PageModel pageModel, CoinextScreen coinextScreen) {
        return extendedCoinextService.pageQuery(pageModel, coinextScreen);
    }

    /**
     * Merge message result.
     *
     * @param coinext       the coinext
     * @param bindingResult the binding result
     * @return the message result
     */
    @PostMapping("/merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create/modify currency extensions")
    public MessageResult merge(@Valid Coinext coinext, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return extendedCoinextService.merge(coinext);
    }

}

package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.SysPermission;
import com.icetea.lotus.service.system.ExtendedSysPermissionService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * The type Permission controller.
 */
@Slf4j
@RestController
@RequestMapping("/system/permission")
@RequiredArgsConstructor
public class PermissionController {

    private final ExtendedSysPermissionService extendedSysPermissionService;

    /**
     * Merge message result.
     *
     * @param permission the permission
     * @return the message result
     */
    @PreAuthorize("hasRole('system:permission:merge')")
    @PostMapping("/merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create/modify permissions")
    public MessageResult merge(@Valid SysPermission permission) {
        return extendedSysPermissionService.merge(permission);
    }


    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @param parentId  the parent id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:permission:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Paging query permissions")
    public MessageResult pageQuery(PageModel pageModel,
                                   @RequestParam(value = "parentId", required = false) Long parentId) {

        return extendedSysPermissionService.pageQuery(pageModel, parentId);
    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:permission:detail')")
    @PostMapping("/detail")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Permission details")
    public MessageResult detail(@RequestParam(value = "id") Long id) {
        return extendedSysPermissionService.detail(id);
    }

    /**
     * Deletes message result.
     *
     * @param ids the ids
     * @return the message result
     */
    @PreAuthorize("hasRole('system:permission:deletes')")
    @PostMapping("/deletes")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Batch Delete Permission")
    public MessageResult deletes(@RequestParam(value = "ids") Long[] ids) {
        return extendedSysPermissionService.deletes(ids);
    }

}

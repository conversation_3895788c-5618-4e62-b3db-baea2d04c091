package com.icetea.lotus.controller.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.TransferAddress;
import com.icetea.lotus.model.screen.TransferAddressScreen;
import com.icetea.lotus.service.system.ExtendedTransferAddressService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * The type Transfer address controller.
 */
@RestController
@RequestMapping("/system/transfer-address")
@RequiredArgsConstructor
public class TransferAddressController {

    private final ExtendedTransferAddressService extendedTransferAddressService;

    /**
     * Merge message result.
     *
     * @param transferAddress the transfer address
     * @param coinName        the coin name
     * @return the message result
     */
    @PreAuthorize("hasRole('system:transfer-address:merge')")
    @PostMapping("merge")
    public MessageResult merge(@Valid TransferAddress transferAddress , @RequestParam("coinName") String coinName){

        return extendedTransferAddressService.merge(transferAddress, coinName);
    }

    /**
     * Page query message result.
     *
     * @param pageModel             the page model
     * @param transferAddressScreen the transfer address screen
     * @return the message result
     */
    @PreAuthorize("hasRole('system:transfer-address:page-query')")
    @PostMapping("page-query")
    public MessageResult pageQuery(PageModel pageModel, TransferAddressScreen transferAddressScreen){

        return extendedTransferAddressService.pageQuery(pageModel, transferAddressScreen);
    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:transfer-address:detail')")
    @PostMapping("detail")
    public MessageResult detail(Long id){
        return extendedTransferAddressService.detail(id);
    }

    /**
     * Deletes message result.
     *
     * @param ids the ids
     * @return the message result
     */
    @PreAuthorize("hasRole('system:transfer-address:deletes')")
    @PostMapping("deletes")
    public MessageResult deletes(Long[] ids){
        return extendedTransferAddressService.deletes(ids);
    }

}

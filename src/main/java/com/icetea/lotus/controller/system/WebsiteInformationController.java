package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.entity.spot.WebsiteInformation;
import com.icetea.lotus.service.system.ExtendedWebsiteInformationService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system/website-information")
@RequiredArgsConstructor
public class WebsiteInformationController {

    private final ExtendedWebsiteInformationService extendedWebsiteInformationService;

    /**
     * Get message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('system:website-information:find-one')")
    @GetMapping("/find-one")
    @AccessLog(module = AdminModule.SYSTEM, operation = "WebsiteInformation")
    public MessageResult get() {
        return extendedWebsiteInformationService.get();
    }


    /**
     * Modify message result.
     *
     * @param websiteInformation the website information
     * @return the message result
     */
    @PreAuthorize("hasRole('system:website-information:alter')")
    @PutMapping("/alter")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Update site information WebsiteInformation")
    public MessageResult modify(WebsiteInformation websiteInformation) {
        return extendedWebsiteInformationService.modify(websiteInformation);

    }

}

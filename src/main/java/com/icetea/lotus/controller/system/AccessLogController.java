package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.service.system.ExtendedAdminAccessLogService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * The type Access log controller.
 */
@Slf4j
@RestController
@RequestMapping("/system/access-log")
@RequiredArgsConstructor
public class AccessLogController {

    private final ExtendedAdminAccessLogService extendedAdminAccessLogService;

    /**
     * Query all admin activities logs.
     *
     * @return A MessageResult containing all admin access logs.
     */
    @PreAuthorize("hasRole('system:access-log:all')")
    @GetMapping("/all")
    @AccessLog(module = AdminModule.SYSTEM, operation = "All operations/access logs AdminAccessLog")
    public MessageResult all() {
        return extendedAdminAccessLogService.all();
    }

    /**
     * Get the details of a specific admin access log by its ID.
     *
     * @param id The ID of the admin access log.
     * @return A MessageResult containing the details of the specified access log.
     */
    @PreAuthorize("hasRole('system:access-log:detail')")
    @GetMapping("/{id}")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Operation/AccessLog Details")
    public MessageResult detail(@PathVariable("id") Long id) {
        return extendedAdminAccessLogService.detail(id);
    }

    /**
     * Query paginated admin access logs with optional filters.
     *
     * @param pageModel The pagination and sorting information.
     * @param adminName (Optional) The admin username to filter the logs.
     * @param module    (Optional) The system module to filter the logs.
     * @return A MessageResult containing the paginated list of admin access logs.
     */
    @PreAuthorize("hasRole('system:access-log:page-query')")
    @GetMapping("/page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Pagination Find Operation/AccessLog")
    public MessageResult pageQuery(
            PageModel pageModel,
            @RequestParam(value = "adminName", required = false) String adminName,
            @RequestParam(value = "module", required = false) AdminModule module) {

            return extendedAdminAccessLogService.pageQuery(pageModel, adminName, module);
    }
}

package com.icetea.lotus.controller.system;

import com.icetea.lotus.constant.AdminStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.common.constants.AdminRoleEnum;
import com.icetea.lotus.service.system.AdminService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/system/admin")
@Slf4j
@RequiredArgsConstructor
public class AdminController extends BaseController {

    private final AdminService adminService;

    @PreAuthorize("hasRole('admin-user-management:view')")
    @GetMapping("/search")
    public MessageResult getAdmins(@RequestParam(required = false) String keyword,
                                   @RequestParam(required = false) Integer roleId,
                                   @RequestParam(required = false) AdminStatus status,
                                   PageModel pageModel) {
        return adminService.getAdmins(keyword, roleId, status, pageModel);
    }

    @GetMapping("/roles")
    public MessageResult getAdminRoles() {
        List<Map<String, Object>> roles = Arrays.stream(AdminRoleEnum.values())
                .map(role -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", role.getId());
                    map.put("description", role.getDescription());
                    return map;
                }).toList();
        return success(roles);
    }

    @GetMapping("/statuses")
    public MessageResult getAdminStatuses() {
        List<Map<String, Object>> statuses = Arrays.stream(AdminStatus.values())
                .map(status -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", status.ordinal());
                    map.put("description", status.getName());
                    return map;
                }).toList();
        return success(statuses);
    }
}

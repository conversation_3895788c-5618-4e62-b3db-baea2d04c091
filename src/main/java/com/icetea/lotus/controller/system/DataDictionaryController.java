package com.icetea.lotus.controller.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.create.DataDictionaryCreate;
import com.icetea.lotus.model.update.DataDictionaryUpdate;
import com.icetea.lotus.service.system.ExtendedDataDictionaryService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * The type Data dictionary controller.
 */
@Slf4j
@RestController
@RequestMapping("system/data-dictionary")
@RequiredArgsConstructor
public class DataDictionaryController {

    private final ExtendedDataDictionaryService extendedDataDictionaryService;

    /**
     * Post message result.
     *
     * @param model         the model
     * @param bindingResult the binding result
     * @return the message result
     */
    @PostMapping
    public MessageResult post(@Valid DataDictionaryCreate model, BindingResult bindingResult) {
        log.info(">>>>>>> Add key-value pairs>>>key>>"+model.getBond()+">>>>value>>"+model.getValue());
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return extendedDataDictionaryService.post(model);
    }

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
    @GetMapping
    public MessageResult page(PageModel pageModel) {
        return extendedDataDictionaryService.page(pageModel);

    }

    /**
     * Put message result.
     *
     * @param bond  the bond
     * @param model the model
     * @return the message result
     */
    @PreAuthorize("hasRole('system:data-dictionary:update')")
    @PutMapping("/{bond}")
    public MessageResult put(@PathVariable("bond") String bond, DataDictionaryUpdate model) {
        log.info(">>>>>Modify key-value pairs>>>key>>"+bond+">>>value>>"+model.getValue());
        return extendedDataDictionaryService.put(bond, model);

    }


}

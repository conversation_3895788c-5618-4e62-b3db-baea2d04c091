package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.ActivityRewardType;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.RewardActivitySetting;
import com.icetea.lotus.service.system.ExtendedRewardActivitySettingService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * The type Reward activity record controller.
 */
@RestController
@RequestMapping("/system/reward-activity-record")
@RequiredArgsConstructor
public class RewardActivityRecordController {

    private final ExtendedRewardActivitySettingService extendedRewardActivitySettingService;

    /**
     * Merge message result.
     *
     * @param setting the setting
     * @return the message result
     */
    @PreAuthorize("hasRole('system:reward-activity-record:merge')")
    @PostMapping("merge")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create and modify invitation reward settings")
    public MessageResult merge(@Valid RewardActivitySetting setting) {
        return extendedRewardActivitySettingService.merge(setting);
    }

    /**
     * Query all undisabled (judgment type conditions)
     * Descending order by default
     *
     * @param pageModel the page model
     * @param type      the type
     * @return message result
     */
    @PreAuthorize("hasRole('system:reward-activity-record:page-query')")
    @GetMapping("page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Pagination query invitation reward settings")
    public MessageResult pageQuery(PageModel pageModel,
                                   @RequestParam(value = "type", required = false) ActivityRewardType type) {

        return extendedRewardActivitySettingService.pageQuery(pageModel, type);
    }

    /**
     * Deletes message result.
     *
     * @param ids the ids
     * @return the message result
     */
    @PreAuthorize("hasRole('system:reward-activity-record:deletes')")
    @DeleteMapping("deletes")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Batch Delete Invitation Reward Settings")
    public MessageResult deletes(Long[] ids) {
        return extendedRewardActivitySettingService.deletes(ids);
    }
}

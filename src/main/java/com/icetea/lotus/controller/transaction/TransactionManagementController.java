package com.icetea.lotus.controller.transaction;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.dto.TransferRecordDTO;
import com.icetea.lotus.dto.request.AdminHistoryWithdrawRequest;
import com.icetea.lotus.dto.request.DepositScreenRequest;
import com.icetea.lotus.dto.response.DepositRecordResponse;
import com.icetea.lotus.service.finance.ExtendedDepositService;
import com.icetea.lotus.service.transaction.TransactionManagementService;
import com.icetea.lotus.util.ExcelUtil;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/management")
@RequiredArgsConstructor
public class TransactionManagementController extends BaseController {

    private final TransactionManagementService transactionManagementService;
    private final ExtendedDepositService extendedDepositService;

    @PreAuthorize("hasRole('transaction-management:view')")
    @PostMapping("/withdraw/page-query")
    public MessageResult getAllWithdrawHistory(@RequestBody AdminHistoryWithdrawRequest adminHistoryWithdrawRequest) {
        return success(transactionManagementService.getAllWithdrawHistory(adminHistoryWithdrawRequest));
    }

    @PreAuthorize("hasRole('transaction-management:export')")
    @PostMapping("/withdraw/export")
    public void exportWithdrawHistory(@RequestBody AdminHistoryWithdrawRequest request, HttpServletResponse response) {
         transactionManagementService.exportWithdrawHistory(request, response);
    }


    /**
     * Get paginated transfer records with filters.
     *
     * @param request     filter conditions for transfer records
     * @return paginated result of transfer records
     */
    @PreAuthorize("hasRole('transaction-management:view')")
    @PostMapping("/transfer/page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Pagination query transfer record")
    public MessageResult getAllTransferHistory(
            @RequestBody TransferRecordDTO request
    ) {
        log.info("TransferRecord PageQuery called with filters: {}", request);
        return success(transactionManagementService.getAllTransferHistory(request));
    }

    @PreAuthorize("hasRole('transaction-management:export')")
    @PostMapping("/transfer/export")
    public void exportTransferHistory(@RequestBody TransferRecordDTO request, HttpServletResponse response) {
        transactionManagementService.exportTransferHistory(request, response);
    }


    /**
     * Retrieves a paginated list of deposit records with the given filter criteria.
     *
     * @param depositScreenRequest The filter criteria for querying deposit records (e.g., currency, member ID).
     * @return MessageResult containing the paginated list of deposit records based on the filter criteria.
     * @throws IOException If an error occurs while writing the response.
     */
    @PreAuthorize("hasRole('transaction-management:view')")
    @PostMapping("/deposit/page-query")
    public Page<DepositRecordResponse> pageQuery(@RequestBody DepositScreenRequest depositScreenRequest) throws IOException {
        return extendedDepositService.pageQuery(depositScreenRequest);
    }

    @PreAuthorize("hasRole('transaction-management:export')")
    @PostMapping("/deposit/export")
    public void exportDepositHistory(@RequestBody DepositScreenRequest screen,
                                      HttpServletResponse response) throws IOException {
        int page = 1;
        int size = 100;
        List<DepositRecordResponse> listAll = new ArrayList<>();
        Page<DepositRecordResponse> responseData;

        do {
            screen.setPageNo(page);
            screen.setPageSize(size);

            responseData = extendedDepositService.pageQuery(screen);
            log.info("Data response {}", responseData.getContent());
            listAll.addAll(responseData.getContent());

            page++;
        } while (page <= responseData.getTotalPages());

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=deposit-history.xlsx");

        try {
            ExcelUtil.listToExcel(listAll, DepositRecordResponse.class.getDeclaredFields(), response.getOutputStream());
        } catch(IOException e) {
            log.error("Error export deposit history: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to fetch deposit history", e);
        }
    }
}

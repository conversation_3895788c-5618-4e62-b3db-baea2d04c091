package com.icetea.lotus.controller.common;

import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * The type Base admin controller.
 *
 * <AUTHOR>
 * @date
 */
@Component
@RequiredArgsConstructor
public class BaseAdminController extends BaseController {

    private final BaseAdminService baseAdminService;

    /**
     * Gets admin.
     *
     * @param request the request
     * @return the admin
     */
    protected Admin getAdmin(HttpServletRequest request) {
        return baseAdminService.getAdmin(request);
    }

    /**
     * Determine whether the mobile phone verification code is correct
     *
     * @param code Verification code
     * @param key  Key prefix + mobile phone number in redis
     * @return message result
     */
    protected MessageResult checkCode(String code, String key) {
        return baseAdminService.checkCode(code, key);
    }
}

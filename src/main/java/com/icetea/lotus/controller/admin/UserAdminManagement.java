package com.icetea.lotus.controller.admin;

import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.dto.request.EditUserRequest;
import com.icetea.lotus.dto.request.RegisterRequest;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.service.admin.AdminManagementService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@RestController
@RequestMapping("user")
@RequiredArgsConstructor
public class UserAdminManagement {

    private final AdminManagementService adminManagementService;

    /**
     * Process terminate user admin by admin's userId
     *
     * @param userId request param
     * @return MessageResult
     */
    @PreAuthorize("hasRole('admin-user-management:remove-user')")
    @DeleteMapping("/terminate")
    public MessageResult terminateUserAdmin(@RequestParam Long userId, @CurrentUser AuthAdmin user) {
        return adminManagementService.terminateAdmin(userId, user);
    }

    /**
     * Locks or unlocks a user by ID.
     *
     * @param id   the user's ID
     * @param lock true to lock, false to unlock
     * @return the operation result
     */
    @PreAuthorize("hasRole('admin-user-management:lock-user')")
    @PostMapping("/{id}/locking")
    public MessageResult lockUser(@PathVariable Long id, @RequestParam boolean lock, @CurrentUser AuthAdmin currentUser) {
        return adminManagementService.lockUser(id, lock, currentUser);
    }

    /**
     * user admin
     *
     * @param registerRequest data request
     * @return message
     */
    @PreAuthorize("hasRole('admin-user-management:add-user')")
    @PostMapping("/add-new-member")
    public MessageResult addNewMember(@RequestBody RegisterRequest registerRequest) {
        return adminManagementService.addNewMember(registerRequest);
    }

    /**
     * Edits an existing user's information.
     * <p>
     * Validates the request data from {@link EditUserRequest}. If validation fails,
     * returns errors; otherwise, updates the user info.
     * </p>
     *
     * @param editRequest user information to update
     * @param bindingResult holds validation errors
     * @return {@link MessageResult} indicating success or validation failure
     */
    @PreAuthorize("hasRole('admin-user-management:update-user')")
    @PutMapping("/edit")
    public MessageResult editUser(@Valid @RequestBody EditUserRequest editRequest, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return adminManagementService.editUser(editRequest);
    }

}

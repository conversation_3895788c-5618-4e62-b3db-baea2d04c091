package com.icetea.lotus.controller.admin;

import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.dto.request.AdminWalletListRequest;
import com.icetea.lotus.dto.request.AdminWalletTransferRequest;
import com.icetea.lotus.dto.request.WalletTransactionHistoryRequest;
import com.icetea.lotus.dto.response.ListingAdminWalletResponse;
import com.icetea.lotus.entity.transform.AuthAdmin;
import com.icetea.lotus.service.admin.AdminWalletManagementService;
import com.icetea.lotus.util.MessageResult;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("wallet/management")
@RequiredArgsConstructor
public class AdminWalletManagement {
    private final AdminWalletManagementService adminWalletManagementService;

    @PreAuthorize("hasRole('wallet-management:view-wallet-list')")
    @PostMapping("listing")
    public MessageResult listingWallet(@RequestBody AdminWalletListRequest screen) {
        Page<ListingAdminWalletResponse> walletList = adminWalletManagementService.listingWallet(screen);
        return MessageResult.success("Get wallet by user admin successfully", walletList);
    }

    @PreAuthorize("hasRole('wallet-management:internal-transfer')")
    @PostMapping("transfer")
    public MessageResult transfer(@CurrentUser AuthAdmin admin, @RequestBody @Valid AdminWalletTransferRequest request) {
        return adminWalletManagementService.transfer(admin, request);
    }

    @PreAuthorize("hasRole('wallet-management:view-transaction-history')")
    @PostMapping("transaction-history")
    public MessageResult transactionHistory(@RequestBody WalletTransactionHistoryRequest screen) {
        return adminWalletManagementService.transactionHistory(screen);
    }
}

package com.icetea.lotus.controller.exchange;

import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.PairEditRequest;
import com.icetea.lotus.dto.request.PairListRequest;
import com.icetea.lotus.dto.request.PairTradingStatusRequest;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.admin.AdminWalletManagementService;
import com.icetea.lotus.service.exchange.PairManagementService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;


@RestController
@RequestMapping("/exchange/pair-management")
@RequiredArgsConstructor
public class PairManagementController {
    private final PairManagementService pairManagementService;
    private final AdminWalletManagementService adminWalletManagementService;

    /**
     * Retrieves a paginated list of pairs based on the provided filters.
     *
     * @param pageModel       the pagination and sorting information
     * @param pairListRequest the filter and search criteria for pairs
     * @return MessageResult containing the paginated pair list and metadata
     */
    @PreAuthorize("hasRole('pair-management:view')")
    @GetMapping("/page-query")
    public MessageResult getPairList(PageModel pageModel, PairListRequest pairListRequest) {
        return pairManagementService.getPairList(pageModel, pairListRequest);
    }

    /**
     * Get pair detail (include information related to general)
     *
     * @param symbol          the pair symbol
     * @param httpServletRequest the http servlet request
     * @return the message result
     */
    @PreAuthorize("hasRole('pair-management:view-detail')")
    @GetMapping("/detail/general")
    public MessageResult getGeneralPairDetail(@RequestParam String symbol, HttpServletRequest httpServletRequest) {
        return pairManagementService.getGeneralPairDetail(symbol, httpServletRequest);
    }

    /**
     * Get pair detail (include information related to trading)
     *
     * @param symbol          the pair symbol
     * @param typeTrading     the type trading
     * @return the message result
     */
    @PreAuthorize("hasRole('pair-management:view-detail')")
    @GetMapping("/detail/trading")
    public MessageResult getPairDetailOfTypeTrading(@RequestParam String symbol, @RequestParam String typeTrading) {
        return pairManagementService.getPairDetailOfTypeTrading(symbol, typeTrading);
    }

    /**
     * Edit an existing trading pair.
     *
     * @param pairEditRequest the request body containing pair update details
     * @param bindingResult   the validation result of the request body
     * @return MessageResult   success or error message after editing the pair
     */
    @PreAuthorize("hasRole('pair-management:update-pair')")
    @PutMapping("/edit")
    public MessageResult editPair(@Valid @RequestBody PairEditRequest pairEditRequest, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return pairManagementService.editPair(pairEditRequest);
    }

    /**
     * Update the trading status of a pair.
     *
     * @param typeTrading the type of trading for the pair
     * @param request     the request body containing the pair trading status update details
     * @return MessageResult success or error message after updating the trading status
     */
    @PreAuthorize("hasRole('pair-management:lock-pair')")
    @PutMapping("/trading-status/{typeTrading}")
    public MessageResult updateTradingStatus(@PathVariable String typeTrading, @Valid @RequestBody PairTradingStatusRequest request) {
        return pairManagementService.updateTradingStatus(typeTrading, request.getSymbol(), request.getEnable());
    }

    @GetMapping("/pnl-summary")
    public MessageResult getPnlSummary(@RequestParam String symbol) {
        return pairManagementService.getRequiredPnlSummary(symbol);
    }

    @GetMapping("/check-sufficient-balance")
    public MessageResult checkSufficientBalance(@CurrentUser AuthMember user, @RequestParam BigDecimal requiredBalance) {
        return adminWalletManagementService.checkSufficientBalance(user, requiredBalance);
    }

    

}

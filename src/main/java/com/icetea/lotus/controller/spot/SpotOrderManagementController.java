package com.icetea.lotus.controller.spot;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.SpotOrderListRequest;
import com.icetea.lotus.service.spot.SpotOrderManagementService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/management/spot-order")
@RequiredArgsConstructor
@Slf4j
public class SpotOrderManagementController {

    private final SpotOrderManagementService spotOrderManagementService;

    @PreAuthorize("hasRole('spot-order-management:view')")
    @PostMapping("/page-query")
    public MessageResult pageQuery(
            @RequestBody SpotOrderListRequest spotOrderListRequest
    ) {
        log.info("Spot orders PageQuery called with filters: {}", spotOrderListRequest);

        return spotOrderManagementService.pageQuery(spotOrderListRequest);
    }

    @PreAuthorize("hasRole('spot-order-management:view-detail-order')")
    @GetMapping("/detail/{orderId}")
    public MessageResult getOrderDetail(@PathVariable String orderId) {
        return spotOrderManagementService.getOrderDetail(orderId);
    }

    /**
     * Retrieves all trades associated with a specific spot order.
     *
     * @param orderId the unique identifier of the spot order
     * @param pageModel the pagination parameters including page number and size
     * @return MessageResult containing the order's trade history
     */
    @PreAuthorize("hasRole('spot-order-management:view-detail-order')")
    @GetMapping("/detail/{orderId}/trades")
    public MessageResult getOrderTrades(@PathVariable String orderId, @ModelAttribute PageModel pageModel) {
        return spotOrderManagementService.getOrderTrades(orderId, pageModel);
    }

    @GetMapping("/statuses")
    public MessageResult getListOrderStatus() {
        return spotOrderManagementService.getListOrderStatus();
    }

    @GetMapping("/types")
    public MessageResult getListOrderType() {
        return spotOrderManagementService.getListOrderType();
    }

    @GetMapping("/bot-transaction")
    public MessageResult getListBotTransactionStatus() {
        return spotOrderManagementService.getBotTransaction();
    }
}

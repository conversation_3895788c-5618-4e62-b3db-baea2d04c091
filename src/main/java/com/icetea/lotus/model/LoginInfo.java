package com.icetea.lotus.model;

import com.icetea.lotus.constant.AdminStatus;
import com.icetea.lotus.constant.FirstTimeLoginStatus;
import com.icetea.lotus.core.Menu;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.SysPermission;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class LoginInfo {
    private String username;
    private Date lastLoginTime;
    private Date terminatedTime;
    private String lastLoginIp;
    private Long roleId;
    private String realName;
    private String mobilePhone;
    private String email;
    private String avatar;
    private AdminStatus status;
    private FirstTimeLoginStatus firstTimeLoginStatus;
    private List<Menu> menuList;

    public static LoginInfo getLoginInfo(Admin admin, List<Menu> getRole) {
        return LoginInfo.builder()
                .username(admin.getUsername())
                .lastLoginTime(admin.getLastLoginTime())
                .terminatedTime(admin.getTerminatedTime())
                .lastLoginIp(admin.getLastLoginIp())
                .roleId(admin.getRoleId())
                .realName(admin.getRealName())
                .mobilePhone(admin.getMobilePhone())
                .email(admin.getEmail())
                .avatar(admin.getAvatar())
                .status(admin.getStatus())
                .firstTimeLoginStatus(admin.getFirstTimeLoginStatus())
                .menuList(getRole)
                .build();
    }
}

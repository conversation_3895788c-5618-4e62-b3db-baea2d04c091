package com.icetea.lotus.model.create;

import com.icetea.lotus.ability.CreateAbility;
import com.icetea.lotus.entity.spot.DataDictionary;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR> @Title: ${file_name}
 * @Description:
 * @date 2019/4/1214:24
 */
@Data
public class DataDictionaryCreate implements CreateAbility<DataDictionary> {
    @NotBlank
    private String bond;
    @NotBlank
    private String value;
    private String comment;

    @Override
    public DataDictionary transformation() {
        return new DataDictionary(bond, value, comment);
    }
}

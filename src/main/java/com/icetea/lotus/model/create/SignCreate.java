package com.icetea.lotus.model.create;

import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.Sign;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.SignService;
import com.icetea.lotus.util.DateUtil;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import org.springframework.util.Assert;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> @Description:
 * @date 2019/5/311:24
 */
@Data
public class SignCreate {

    /**
     * Gift currency
     */
    @NotBlank(message = "unit can not be empty!")
    private String unit;

    /**
     * Number of gifts
     */
    @Min(value = 0, message = "amount must gt 0!")
    private BigDecimal amount;

    /**
     * End date
     */
    @NotNull(message = "endDate can not be null!")
    private Date endDate;

    public Sign transformation(SignService signService, CoinService coinService) {
        // Check whether the check-in is in progress
        Sign underway = signService.fetchUnderway();
        Assert.isNull(underway, "validate underway!");

        // Verify the existence of currency
        Coin coin = coinService.findByUnit(unit);
        Assert.notNull(coin, "validate unit!");

        // Verification time
        DateUtil.validateEndDate(endDate);

        // Return object
        Sign sign = new Sign();
        sign.setAmount(amount);
        sign.setCoin(coin);
        sign.setEndDate(endDate);

        return sign;
    }
}

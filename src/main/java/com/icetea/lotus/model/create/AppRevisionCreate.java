package com.icetea.lotus.model.create;

import com.icetea.lotus.ability.CreateAbility;
import com.icetea.lotus.constant.Platform;
import com.icetea.lotus.entity.spot.AppRevision;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> @Title: ${file_name}
 * @Description:
 * @date 2019/4/2416:33
 */
@Data
public class AppRevisionCreate implements CreateAbility<AppRevision> {

    private String remark;

    @NotBlank
    private String version;

    private String downloadUrl;

    @NotNull
    private Platform platform;

    // Conversion
    @Override
    public AppRevision transformation() {
        AppRevision appRevision = new AppRevision();
        appRevision.setRemark(this.remark);
        appRevision.setVersion(this.version);
        appRevision.setDownloadUrl(this.downloadUrl);
        appRevision.setPlatform(this.platform);
        return appRevision;
    }
}

package com.icetea.lotus.model.update;

import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.Sign;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.util.DateUtil;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import org.springframework.util.Assert;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> @Title: ${file_name}
 * @Description:
 * @date 2019/5/314:21
 */
@Data
public class SignUpdate {
    /**
     * Gift currency
     */
    @NotBlank(message = "unit can not be empty!")
    private String unit;

    /**
     * Number of gifts
     */
    @Min(value = 0, message = "amount must gt 0!")
    private BigDecimal amount;



    /**
     * End date
     */
    @NotNull(message = "endDate can not be null!")
    private Date endDate;

    /**
     * @param coinService
     * @param sign Persistent
     * @return
     */
    public Sign transformation(CoinService coinService, Sign sign) {
        Coin coin = coinService.findByUnit(unit);
        Assert.notNull(coin, "validate unit!");
        // Verification time
        DateUtil.validateEndDate(endDate);
        sign.setCoin(coin);
        sign.setEndDate(endDate);
        sign.setAmount(amount);
        return sign;
    }

}

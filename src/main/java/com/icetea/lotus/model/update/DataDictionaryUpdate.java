package com.icetea.lotus.model.update;

import com.icetea.lotus.ability.UpdateAbility;
import com.icetea.lotus.entity.spot.DataDictionary;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR> @Title: ${file_name}
 * @Description:
 * @date 2019/4/1214:46
 */
@Data
public class DataDictionaryUpdate implements UpdateAbility<DataDictionary> {
    @NotBlank
    private String value;
    private String comment;

    @Override
    public DataDictionary transformation(DataDictionary dataDictionary) {
        dataDictionary.setValue(value);
        dataDictionary.setComment(comment);
        return dataDictionary;
    }
}

package com.icetea.lotus.model.exchange;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExchangeCoinRateRequest {
    private String symbol;
    private BigDecimal fee;
    private BigDecimal maxBuyPrice;
    private BigDecimal minTurnover;
    private Integer enable; // 1: top, 2
    private Integer visible; // 1: Yes, 2
    private Integer exchangeable; // 1: Yes, 2
    private Integer enableMarketBuy; // 1:Yes, 0:No
    private Integer enableMarketSell; // 1:Yes, 0:No
    private Integer enableBuy; // 1:Yes, 0:No
    private Integer enableSell; // 1:Yes, 0:No
    private Integer flag; // 1:Yes, 0:No
    private Integer fakeDataStatus; // 0: shut down, 1: start
    private Integer sort;
    private String password;

}

package com.icetea.lotus.model.exchange;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class RobotConfigRequest {
    private String symbol;
    private Integer isHalt;
    private Double startAmount;
    private Double randRange0;
    private Double randRange1;
    private Double randRange2;
    private Double randRange3;
    private Double randRange4;
    private Double randRange5;
    private Double randRange6;
    private Integer scale;
    private Integer amountScale;
    private BigDecimal maxSubPrice;
    private Integer initOrderCount;
    private BigDecimal priceStepRate;
    private Integer runTime;
}

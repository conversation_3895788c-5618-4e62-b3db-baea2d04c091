package com.icetea.lotus.model.screen;

import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.CommonStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class MemberScreen extends AccountScreen{

    /**
     * Member registration time
     */

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date startTime;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * Certified merchant status: (012 Not certified/reviewed/certified)
     */
    private CertifiedBusinessStatus status;
    /**
     * 01 (Normal/Illegal)
     */
    private CommonStatus commonStatus ;

    /**
     * Agent: 0 No, 1 Yes
     */
    private String superPartner="0";
}

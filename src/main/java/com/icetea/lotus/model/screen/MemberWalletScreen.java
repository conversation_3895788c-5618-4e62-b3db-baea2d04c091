package com.icetea.lotus.model.screen;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class MemberWalletScreen extends AccountScreen{

    String unit ;

    String walletAddress ;

    BigDecimal minBalance ;

    BigDecimal maxBalance ;

    BigDecimal minFrozenBalance;

    BigDecimal maxFrozenBalance ;

    BigDecimal minAllBalance ;

    BigDecimal maxAllBalance ;

    Integer isOut;
}

package com.icetea.lotus.model.screen;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.WithdrawStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class WithdrawRecordScreen extends AccountScreen{

    private String unit ;

    /**
     * Withdrawal address
     */
    private String address ;

    private WithdrawStatus status ;

    /**
     * Whether to withdraw cash automatically
     */
    private BooleanEnum isAuto;

    private Long memberId ;

    private String mobilePhone;

    private String orderSn;
}

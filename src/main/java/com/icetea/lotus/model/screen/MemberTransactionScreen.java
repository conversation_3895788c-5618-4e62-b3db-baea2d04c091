package com.icetea.lotus.model.screen;

import com.icetea.lotus.constant.TransactionType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class MemberTransactionScreen extends AccountScreen{

    /**
     * Trading time search
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date startTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endTime;
    /**
     * RECHARGE("Recharge"),
     * WITHDRAW("withdrawal"),
     * TRANSFER_ACCOUNTS("Transfer"),
     * EXCHANGE("Coin Trading"),
     * OTC_BUY("Fiat Coin Buy"),
     * OTC_SELL("Fiat currency sell"),
     * ACTIVITY_AWARD("Activity Reward"),
     * PROMOTION_AWARD("Promotion Reward"),
     * DIVIDEND("Diployment"),
     * VOTE("vote"),
     * ADMIN_RECHARGE("Manual Recharge"),
     * MATCH("pair");
     * ACTIVITY_BUY("Activity redemption");
     */
    private TransactionType type;

    /**
     * Transaction amount search
     */
    private BigDecimal minMoney;
    private BigDecimal maxMoney;

    /**
     * Processing fee search
     */
    private BigDecimal minFee;
    private BigDecimal maxFee;

    private Long memberId;


    // Type (0: transaction details, 1: handling fee details)
    private Integer outType;

    // Whether to export (0: No, 1: Yes)
    private Integer isOut;

    private String symbol;
}

package com.icetea.lotus.model.screen;

import com.icetea.lotus.constant.AdvertiseControlStatus;
import com.icetea.lotus.constant.AdvertiseType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdvertiseScreen extends AccountScreen{

    AdvertiseType advertiseType;

    String payModel ;

    /**
     * Advertising status (012 Listed/Offset/Close)
     */
    AdvertiseControlStatus status ;

}

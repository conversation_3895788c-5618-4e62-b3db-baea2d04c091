package com.icetea.lotus.model.screen;

import com.icetea.lotus.constant.AdvertiseType;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.OrderStatus;
import lombok.Data;

@Data
public class AppealScreen {
    private AdvertiseType advertiseType ;
    private String complainant ;// Complaint
    private String negotiant;// Trader
    private BooleanEnum success;
    private String unit ;
    private OrderStatus status ;
    private Boolean auditing = false ;
}

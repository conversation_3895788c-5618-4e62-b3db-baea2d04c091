package com.icetea.lotus.model.screen;

import com.icetea.lotus.constant.AdvertiseType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderScreen extends OtcOrderTopScreen {
    private Integer isOut;
    private String orderSn;
    private BigDecimal minNumber;
    private BigDecimal maxNumber;
    private String memberName;// Username and real name keywords
    private String customerName;// Username and real name keywords
    private BigDecimal minMoney;
    private BigDecimal maxMoney;
    private AdvertiseType advertiseType;
}

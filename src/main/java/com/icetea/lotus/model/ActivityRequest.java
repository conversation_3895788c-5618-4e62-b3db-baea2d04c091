package com.icetea.lotus.model;

import java.math.BigDecimal;

import com.icetea.lotus.constant.BooleanEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ActivityRequest {
    private Long id;
    private String title;
    private String detail;

    private BooleanEnum status;
    private Integer step;
    private Integer type;

    private String startTime;
    private String endTime;

    private BigDecimal totalSupply;
    private BigDecimal price;

    private Integer priceScale;
    private String unit;
    private String acceptUnit;
    private Integer amountScale;

    private BigDecimal maxLimitAmount;
    private BigDecimal minLimitAmount;
    private Integer limitTimes;

    private String settings;
    private String content;
    private String smallImageUrl;
    private String bannerImageUrl;

    private String noticeLink;
    private String activityLink;

    private Integer levelOneCount;

    private BigDecimal holdLimit;
    private String holdUnit;

    private Integer miningDays;
    private BigDecimal miningDaysProfit;
    private String miningUnit;

    private BigDecimal miningInvite;
    private BigDecimal miningInviteLimit;
    private Integer miningPeriod;

    private String lockedUnit;
    private Integer lockedPeriod;
    private Integer lockedDays;

    private Integer releaseType;
    private BigDecimal releasePercent;
    private BigDecimal lockedFee;
    private BigDecimal releaseAmount;
    private BigDecimal releaseTimes;

    private String password;
}

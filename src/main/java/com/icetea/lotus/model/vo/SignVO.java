package com.icetea.lotus.model.vo;

import com.icetea.lotus.constant.SignStatus;
import com.icetea.lotus.entity.spot.Sign;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> @Title: ${file_name}
 * @Description:
 * @date 2019/5/715:12
 */
@Data
@Builder
public class SignVO {
    private Long id;

    /**
     * Currency name
     */
    private String name;

    /**
     * Currency unit
     */
    private String unit;

    /**
     * Number of gifts
     */
    private BigDecimal amount;

    /**
     * start date
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    /**
     * End date
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    /**
     * Creation time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creationTime;

    /**
     * Sign in activity status
     */
    private SignStatus status;

    public static SignVO getSignVO(Sign x) {
        return SignVO.builder()
                .id(x.getId())
                .name(x.getCoin().getName())
                .unit(x.getCoin().getUnit())
                .amount(x.getAmount())
                .endDate(x.getEndDate())
                .creationTime(x.getCreationTime())
                .status(x.getStatus())
                .build();
    }
}

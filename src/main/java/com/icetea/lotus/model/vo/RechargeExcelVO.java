package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;

@Data
@ExcelSheet(name = "Recharge_Excel_VO")
public class RechargeExcelVO {
    @Excel(name = "User ID")
    private Long memberId;
    @Excel(name = "email")
    private String email;
    @Excel(name = "Mobile phone number")
    private String mobilePhone;
    @Excel(name = "Recharge Currency")
    private String coinname;

    @Excel(name = "Protocol Name")
    private String protocolname;

    @Excel(name = "Coin Recharge Address")
    private String address;

    @Excel(name = "recharge quantity")
    private String money;

    @Excel(name = "status")
    private String status;

    @Excel(name = "confirm number")
    private String confirms;

    @Excel(name = "Access time")
    private String addtime;

}
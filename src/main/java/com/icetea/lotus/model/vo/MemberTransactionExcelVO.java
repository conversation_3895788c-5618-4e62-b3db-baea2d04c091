package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;

@Data
@ExcelSheet(name = "Member_Transaction_Excel_VO")
public class MemberTransactionExcelVO {
    @Excel(name = "Member ID")
    private Long memberId;
    @Excel(name = "Transaction Type")
    private String type;
    @Excel(name = "Transaction Amount")
    private String amount;
    @Excel(name = "Transaction fee")
    private String fee;
    @Excel(name = "Trading Time")
    private String createTime;

}
package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;

@Data
@ExcelSheet(name = "Exchange_Order_Out_VO")
public class ExchangeOrderOutVO {

    @Excel(name = "Order ID")
    private String orderId;
    @Excel(name = "User ID")
    private Long memberId;
    @Excel(name = "Email")
    private String email;
    @Excel(name = "Mobile phone number")
    private String mobilePhone;
    @Excel(name = "Real name")
    private String realName;
    @Excel(name = "Transaction pair")
    private String symbol;
    @Excel(name = "Delegate amount")
    private String amount = "--"; // Convert
    @Excel(name = "Trading Volume")
    private String tradedAmount = "--"; // Convert
    @Excel(name = "Pending Order Type")
    private String type = "--"; // Convert
    @Excel(name = "Order Direction")
    private String direction = "--"; // Convert
    @Excel(name = "Pending order price")
    private String price = "--"; // Convert
    @Excel(name = "Pending order time")
    private String time = "--"; // Convert
    @Excel(name = "status")
    private String status = "--"; // Convert
}
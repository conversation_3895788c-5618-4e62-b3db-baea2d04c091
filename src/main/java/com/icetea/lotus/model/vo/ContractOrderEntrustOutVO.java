package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;

@Data
@ExcelSheet(name = "Contract_Order_Entrust_Out_VO")
public class ContractOrderEntrustOutVO {
    @Excel(name = "User ID")
    private Long memberId;
    @Excel(name = "Mail")
    private String email;
    @Excel(name = "Phone number")
    private String mobilePhone;
    @Excel(name = "Real name")
    private String realName;
    @Excel(name = "Symbol")
    private String symbol;
    @Excel(name = "Direction")
    private String direction; // Convert
    @Excel(name = "Type")
    private String type; // Convert
    @Excel(name = "Number of commissioned numbers")
    private String volume; // Convert
    @Excel(name = "Trigger price")
    private String triggerPrice; // Convert
    @Excel(name = "Entrustment Price")
    private String entrustPrice; // Convert
    @Excel(name = "Transaction price")
    private String tradedPrice; // Convert
    @Excel(name = "Number of transactions")
    private String tradedVolume; // Convert
    @Excel(name = "Opening fee")
    private String openFee; // Convert
    @Excel(name = "Closed position fee")
    private String closeFee; // Convert
    @Excel(name = "Close position profit and loss")
    private String profitAndLoss; // Convert
    @Excel(name = "Status")
    private String status; // Convert
    @Excel(name = "Create time")
    private String createTime; // Convert

}

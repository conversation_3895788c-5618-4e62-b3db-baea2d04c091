package com.icetea.lotus.model.vo;

import lombok.Data;

@Data
public class RechargeVO {

    private Integer id;

    private String hash;

    private String md5;

    private Integer memberid;

    private Long addtime;

    private Long coinid;

    private String coinname;

    private double money;

    private Integer block;

    private Integer confirms;

    private Integer nconfirms;

    // 0 Not arrived 1 Accounted -1 Failed
    private Integer status;

    private String send;

    private String address;

    private Integer protocol;

    private String protocolname;

    private String username;

}

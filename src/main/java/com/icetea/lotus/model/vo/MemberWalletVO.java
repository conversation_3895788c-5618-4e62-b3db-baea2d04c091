package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@ExcelSheet(name = "Member_Wallet_VO")
public class MemberWalletVO {

    private Long id ;

    @Excel(name="user ID")
    private Long memberId = 0L;

    @Excel(name="username")
    private String username = "";

    @Excel(name="email")
    private String email = "";

    @Excel(name="Mobile Number")
    private String mobilePhone = "";

    @Excel(name="Real Name")
    private String realName = "";

    @Excel(name="currency name")
    private String unit = "";

    @Excel(name="Wallet Address")
    private String address = "";

    @Excel(name="number of available coins")
    private String balance = "";

    @Excel(name="Frozen Coins")
    private String frozenBalance = "";

    @Excel(name="Total Coins")
    private String allBalance = "";
}
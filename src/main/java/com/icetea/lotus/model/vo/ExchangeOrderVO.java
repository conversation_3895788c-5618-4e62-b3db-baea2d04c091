package com.icetea.lotus.model.vo;

import com.icetea.lotus.entity.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ExchangeOrderVO {

    private String orderId;
    private Long memberId;
    // Pending order type
    private ExchangeOrderType type;
    // Buy or sell volume, for market price buy order table
    private BigDecimal amount = BigDecimal.ZERO;
    // Trading pair symbols
    private String symbol;
    // Trading volume
    private BigDecimal tradedAmount = BigDecimal.ZERO;
    // Transaction volume is useful for market price pay
    private BigDecimal turnover = BigDecimal.ZERO;
    // Coin Unit
    private String coinSymbol;
    // Settlement unit
    private String baseSymbol;
    // Order Status
    private ExchangeOrderStatus status;
    // Order direction
    private ExchangeOrderDirection direction;
    // Pending order price
    private BigDecimal price = BigDecimal.ZERO;
    // Pending order time
    private Long time;
    // Transaction completion time
    private Long completedTime;
    // Cancel time
    private Long canceledTime;
    // Whether to use discount 0 Not used 1 Use
    private  String useDiscount;
    // Transaction fee has been completed
    private  BigDecimal fee;

    private ExchangeOrderResource orderResource = ExchangeOrderResource.CUSTOMER;

    private List<ExchangeOrderDetail> detail;

    private String email;
    private String mobilePhone;
    private String realName;

}

package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;

@Data
@ExcelSheet(name = "Otc_Order_Excel_VO")
public class OtcOrderExcelVO {
    @Excel(name = "Order Number")
    private String orderSn;
    @Excel(name = "Trading Time")
    private String createTime;
    @Excel(name = "Trader")
    private String customerName;
    @Excel(name = "created by")
    private String memberName;
    @Excel(name = "currency")
    private String unit;
    @Excel(name = "type")
    private String advertiseType;
    @Excel(name = "order quantity")
    private String number;
    @Excel(name = "Order Amount")
    private String money;
    @Excel(name = "processing fee")
    private String fee;
    @Excel(name = "Payment Method")
    private String payMode;
    @Excel(name = "Order Status")
    private String status;

}
package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;

@Data
@ExcelSheet(name = "Member_Transaction_Fee_Excel_VO")
public class MemberTransactionFeeExcelVO {
    @Excel(name = "Member ID")
    private Long memberId;
    @Excel(name = "Transaction Type")
    private String type;
    @Excel(name = "Processing fee type")
    private String symbol;
    @Excel(name = "Trading Time")
    private String createTime;
    @Excel(name = "Transaction fee")
    private String fee;

}
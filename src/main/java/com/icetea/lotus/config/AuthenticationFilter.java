package com.icetea.lotus.config;

import com.icetea.lotus.constant.FirstTimeLoginStatus;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.exception.UnauthorizedException;
import com.icetea.lotus.service.AdminService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
@Configuration
public class AuthenticationFilter extends OncePerRequestFilter {
    private final AdminService adminService;

    public AuthenticationFilter(AdminService adminService) {
        this.adminService = adminService;
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (request.getRequestURI().contains("/login")
                || request.getRequestURI().contains("/health")
                || request.getRequestURI().contains("/logout")
                || request.getRequestURI().contains("/refresh-token")) {
            filterChain.doFilter(request, response);
        } else {
            var hi = (Jwt) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            var username = hi.getClaims().get("email").toString();
            log.info("### Step 1: get username: {}", username);
            Admin admin = adminService.findByEmail(username);
            if (admin != null) {
                if (request.getRequestURI().contains("update-password")) {
                    doFilterChangePw(admin, request, response, filterChain);
                } else if (admin.getFirstTimeLoginStatus().equals(FirstTimeLoginStatus.ACTIVE)) {
                    filterChain.doFilter(request, response);
                } else {
                    throw new UnauthorizedException("Invalid user !!!");
                }
            } else throw new UnauthorizedException("Invalid user !!!");
        }
    }

    private void doFilterChangePw(Admin admin, HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (admin.getFirstTimeLoginStatus().equals(FirstTimeLoginStatus.NEW)) {
            filterChain.doFilter(request, response);
        } else throw new UnauthorizedException("First time login status is active, password has already been changed");
    }
}

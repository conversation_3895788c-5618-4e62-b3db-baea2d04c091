package com.icetea.lotus.config;

import com.icetea.lotus.util.BigDecimalToDecimal128Converter;
import com.icetea.lotus.util.Decimal128ToBigDecimalConverter;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.util.Arrays;

@Configuration
@RequiredArgsConstructor
public class MongoConfig {

    @Bean
    public MongoCustomConversions customConversions() {
        return new MongoCustomConversions(
                Arrays.asList(
                        new BigDecimalToDecimal128Converter(),
                        new Decimal128ToBigDecimalConverter()
                )
        );
    }

    @NotNull
    @Bean
    @Primary
    public MappingMongoConverter mappingMongoConverter(@NotNull MongoDatabaseFactory mongoDatabaseFactory,
                                                       @NotNull MongoCustomConversions mongoCustomConversions,
                                                       @NotNull MongoMappingContext mongoMappingContext) {
        DefaultDbRefResolver dbRefResolver = new DefaultDbRefResolver(mongoDatabaseFactory);
        MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mongoMappingContext);
        converter.setCustomConversions(mongoCustomConversions);
        return converter;
    }

    @NotNull
    @Bean
    public MongoTemplate mongoTemplate(@NotNull MongoDatabaseFactory mongoDatabaseFactory,
                                       @NotNull MappingMongoConverter mappingMongoConverter) {
        return new MongoTemplate(mongoDatabaseFactory, mappingMongoConverter);
    }

    @Bean
    public MongoMappingContext mongoMappingContext() {
        return new MongoMappingContext();
    }

}
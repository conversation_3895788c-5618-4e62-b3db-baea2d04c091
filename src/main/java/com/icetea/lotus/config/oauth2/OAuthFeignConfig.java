package com.icetea.lotus.config.oauth2;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OAuthFeignConfig {


    private final OAuth2AuthorizedClientService clientService;

    @Bean
    public RequestInterceptor oauth2FeignRequestInterceptor() {
        return (RequestTemplate template) -> {
            var authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication instanceof OAuth2AuthenticationToken oauth2Token) {
                OAuth2AuthorizedClient client =
                        clientService.loadAuthorizedClient(oauth2Token.getAuthorizedClientRegistrationId(),
                                oauth2Token.getName());
                if (client != null && client.getAccessToken() != null) {
                    template.header("Authorization", "Bearer " + client.getAccessToken().getTokenValue());
                }
            } else if (authentication instanceof JwtAuthenticationToken jwtToken) {
                Jwt jwt = jwtToken.getToken();
                if (jwt != null) {
                    template.header("Authorization", "Bearer " + jwt.getTokenValue());
                }
            }
        };
    }
}




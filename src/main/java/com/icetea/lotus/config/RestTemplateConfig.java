package com.icetea.lotus.config;

import com.icetea.lotus.config.oauth2.OAuth2RestTemplateFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
@RequiredArgsConstructor
public class RestTemplateConfig {

    private final OAuth2RestTemplateFactory oAuth2RestTemplateFactory;

    public SimpleClientHttpRequestFactory requestFactory() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(Duration.ofSeconds(10));
        requestFactory.setReadTimeout(Duration.ofSeconds(30));
        return requestFactory;
    }

    @Bean
    @LoadBalanced
    RestTemplate restTemplate() {
        var restTemplate = oAuth2RestTemplateFactory.createOAuth2RestTemplate();
        restTemplate.setRequestFactory(requestFactory());
        return restTemplate;
    }

}

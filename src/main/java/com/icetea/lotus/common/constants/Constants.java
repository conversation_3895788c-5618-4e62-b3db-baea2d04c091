package com.icetea.lotus.common.constants;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class Constants {
    public static final Long MAX_LEVERAGE_MULTIPLIER = 5L;

    public static final BigDecimal LEVERAGE_MIN = BigDecimal.ONE;

    public static final Integer MESSAGE_RESULT_SUCCESS_CODE = 0;

    public static final String TRADING_TYPE_SPOT = "spot";

    public static final String TRADING_TYPE_FUTURE = "future";

    public static final Integer ENABLE_STATUS = 1;

    public static final Integer DISABLE_STATUS = 0;

    public static final String SYMBOL_DEFAULT_QUOTE_COIN = "USDT";
}

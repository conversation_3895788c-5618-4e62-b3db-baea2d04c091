package com.icetea.lotus.common.constants;

/**
 * Defines constant method names commonly used for token contracts.
 * Includes method names for retrieving token name, symbol, decimals, and total supply.
 */
public class MethodTokenConstants {
    public static final String METHOD_NAME = "name";
    public static final String METHOD_SYMBOL = "symbol";
    public static final String METHOD_DECIMALS = "decimals";
    public static final String METHOD_TOTAL_SUPPLY = "totalSupply";

    private MethodTokenConstants() {}
}

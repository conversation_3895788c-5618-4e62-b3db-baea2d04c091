package com.icetea.lotus.common.constants;

import lombok.Getter;

@Getter
public enum SpotOrderStatus {

    TRADING("New"),
    COMPLETED("Filled"),
    PARTIAL_FILLED("Partially Filled"),
    TRIGGER("Not triggered"),
    CANCELED("Cancelled");

    private final String displayName;

    SpotOrderStatus(String displayName) {
        this.displayName = displayName;
    }

    public static SpotOrderStatus fromDisplayName(String displayName) {
        for (SpotOrderStatus status : values()) {
            if (status.displayName.equalsIgnoreCase(displayName)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown order status name: " + displayName);
    }
}

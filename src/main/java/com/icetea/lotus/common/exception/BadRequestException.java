package com.icetea.lotus.common.exception;

/**
 * The type Bad request exception.
 */
public class BadRequestException extends RuntimeException {

    private final String message;

    /**
     * Instantiates a new Bad request exception.
     *
     * @param message the message
     */
    public BadRequestException(String message) {
        this.message = message;
    }

    /**
     * Instantiates a new Bad request exception.
     *
     * @param errorCode the error code
     * @param message   the message
     * @param var2      the var 2
     */
    public BadRequestException(String errorCode, String message, Object... var2) {
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

}

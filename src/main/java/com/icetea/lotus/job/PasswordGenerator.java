package com.icetea.lotus.job;

import lombok.NoArgsConstructor;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Generate password when add new user
 * <AUTHOR>
 */
@NoArgsConstructor
public class PasswordGenerator {
    private static final String UPPER = IntStream.rangeClosed('A', 'Z')
            .mapToObj(c -> String.valueOf((char) c))
            .collect(Collectors.joining());

    private static final String LOWER = IntStream.rangeClosed('a', 'z')
            .mapToObj(c -> String.valueOf((char) c))
            .collect(Collectors.joining());
    private static final String DIGITS = "0123456789";
    private static final String SPECIAL = "!@#$%^&*()-_+=<>?";
    private static final String ALL = UPPER + LOWER + DIGITS + SPECIAL;

    private static final SecureRandom random = new SecureRandom();

    public String generatePassword() {
        List<Character> password = new ArrayList<>();

        // Ensure all required types are included
        password.add(UPPER.charAt(random.nextInt(UPPER.length())));
        password.add(LOWER.charAt(random.nextInt(LOWER.length())));
        password.add(DIGITS.charAt(random.nextInt(DIGITS.length())));
        password.add(SPECIAL.charAt(random.nextInt(SPECIAL.length())));

        // Fill remaining with random chars
        for (int i = 4; i < 8; i++) {
            password.add(ALL.charAt(random.nextInt(ALL.length())));
        }

        // Shuffle to make it random
        Collections.shuffle(password);

        // Convert to String
        StringBuilder sb = new StringBuilder();
        for (char c : password) sb.append(c);
        return sb.toString();
    }
}

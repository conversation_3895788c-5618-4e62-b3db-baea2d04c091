package com.icetea.lotus.job;

import com.icetea.lotus.service.WithdrawRecordService;
import com.icetea.lotus.service.notification.EmailService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.vendor.provider.SMSProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Check user withdrawal application
 *
 * <AUTHOR>
@Component
@Slf4j
public class CheckWithdrawJob {

    private final WithdrawRecordService withdrawRecordService;
    private final SMSProvider smsProvider;
    private final EmailService emailService;

    public CheckWithdrawJob(WithdrawRecordService withdrawRecordService, SMSProvider smsProvider, EmailService emailService) {
        this.withdrawRecordService = withdrawRecordService;
        this.smsProvider = smsProvider;
        this.emailService = emailService;
    }

    @Value("${spark.system.admins}")
    private String admins;

    @Value("${spark.system.admin-phones}")
    private String adminPhones;

    /**
     * Check once an hourly
     */
    @Scheduled(cron = "0 0 * * * *")
    public void checkNewWithdrawApplication() {

        long count = withdrawRecordService.countAuditing();
        if (count > 0) {
            try {
                String[] adminList = admins.split(",");
                for (String s : adminList) {
                    emailService.sendEmailMsg(
                            s,
                            "There are new withdrawal applications (total" + count + "strip )",
                            "New withdrawal review notice"
                    );
                }
            } catch (Exception e) {
                processCatchException(e);
            }
        }
    }

    private void processCatchException(Exception e) {
        MessageResult result;
        try {
            String[] phones = adminPhones.split(",");
            if (phones.length > 0) {
                result = smsProvider.sendSingleMessage(phones[0], "==New withdrawal application==");
                if (result.getCode() != 0 && phones.length > 1) {
                    smsProvider.sendSingleMessage(phones[1], "==New withdrawal application==");
                }
            }

        } catch (Exception e1) {
            log.error("Error in CheckWithdrawJob", e1);
            return;
        }
        log.error("Error in CheckWithdrawJob", e);
    }
}
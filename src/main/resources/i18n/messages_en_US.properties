# request
SYSTEM_ERROR_UNEXPECTED=Unexpected error occurred!
REQUEST_FAILED=The request failed!
SUCCESS=success
NO_DATA=No data!
NO_UPDATE=No update!
INVALID_STATUS=Invalid status!
# Audit
AUDIT_PASS=Audit pass!
AUDIT_FAIL=Audit fail!
# other
START_END_TIME=The start time shall not be later than the end time!
TOP_SUCCESS=Top success!
DATA_EXPIRED_LOGIN_AGAIN=Data expired, login again!
# phone
NO_CELL_PHONE_NUMBER=No cell phone number!
SEND_CODE_SUCCESS=The verification code has been sent to the phone!
SEND_CODE_FAILURE_ONE=No repeat verification code within 1 minutes!
CODE_NOT_EXIST_RESEND=Verification code does not exist, please resend!
CODE_ERROR=Verification code error!
CODE_CORRECT=Verification code correct!
NEED_CODE=The verification code needs to be entered!
NO_NEED_CODE=No verification code is required!
# upload
FORMAT_NOT_SUPPORTED=Format not supported!
INVALID_IMAGE_TYPE_MESSAGE=The image must be a file type: {0}
FILE_NOT_FOUND=File not found!
TOKEN_IMAGE_REQUIRED=Token image is required
# coin
COIN_NOT_SUPPORTED=Coin not supported!
PRE_COIN_EXIST=Preselected currencies already exist!
COIN_NAME_EXIST=Currency names already exist!
# password
WRONG_PASSWORD=wrong password!
MODIFY_LATEST=Only the latest ones are allowed to be modified!
# wallet
HOT_WALLET_BALANCE=hot wallet balance :
HOT_WALLET_BALANCE_POOL=The hot wallet is short of balance!
ORDER_NOT_ALLOW_CANCEL=order not allow cancel
#OTC
ORDER_NOT_EXISTS=order not exists
REQUEST_ILLEGAL=reques illegal
ORDER_STATUS_EXPIRED=order status expired

THIS_COIN_HAS_EXISTED=This coin has existed
FAILED_TO_FETCH_TOKEN_CONTRACT=Failed to fetch token contract
FAILED_TO_SAVE_TOKEN_INFO=Failed to save token info
UNSUPPORTED_NETWORK=Unsupported network
INVALID_CONTRACT_ADDRESS=Invalid contract address
UNKNOWN_METHOD_NAME=Unknown method name: {0}
TOKEN_CONTRACT_ADDRESS_HAS_BEEN_USED=Token contract address has been used in another network protocol

COIN_NAME_NOT_EXIST=Coin name not exist!
CUSTOMER_NOT_FOUND=Customer not found with ID: {0}

USER_NOT_FOUND=User not found with ID: {0}
USER_ALREADY_TERMINATED=User already has been terminated.
CANNOT_SELF_LOCK=Locking your own account is not allowed. Please use another account.
EDIT_USER_SUCCESS=Update completed successfully.
LOCK_USER_SUCCESS=Account locked successfully.
UNLOCK_USER_SUCCESS=Account unlocked successfully.
NO_TRADES_FOUND=No trades found for order: {0}
EMAIL_ALREADY_IN_USE=Email already exists
PHONE_ALREADY_IN_USE=Phone number already exists
ROLE_NOT_FOUND=Role not found with id {0}

MEMBER_TRADING_ENABLED= {0} enabled successfully
MEMBER_TRADING_DISABLED= {0} disabled successfully

# Admin Wallet Messages
CANNOT_TRANSFER_TO_SAME_WALLET=Cannot transfer to the same wallet
AT_LEAST_ONE_WALLET_MUST_BE_MASTER=At least one wallet must be a master wallet
INVALID_TRANSFER_AMOUNT=The amount must be greater than 0
INVALID_TRANSFER_FIAT_COIN_AMOUNT=Transfer fiat coin amount must have maximum 2 decimal places
INVALID_TRANSFER_CRYPTO_COIN_AMOUNT=Transfer crypto coin amount must have maximum 8 decimal places
VALIDATE_TRANSFER_REQUEST_FAILED=Validate transfer request failed
AMOUNT_EXCEEDS_THE_LIMITATION=Amount exceeds the limitation
INSUFFICIENT_BALANCE=Insufficient balance in the source wallet. Please try again
INSUFFICIENT_FUND= Insufficient funds. Please add {0} USDT to system wallet
SUFFICIENT_FUND= Sufficient funds.
TRANSFER_FAILED=Transfer failed: {0}

#Pair
PAIR_NOT_FOUND=Pair not found
PAIR_ALREADY_EXISTS=Pair already exists
BASE_COIN_NOT_FOUND=Base coin not found
QUOTE_COIN_NOT_FOUND=Quote coin not found
INVALID_TIMEZONE=Invalid timezone
EDIT_PAIR_SUCCESS=Edit pair successfully
FUTURE_REQUIRED=FUTURE: Cannot enable or edit Future Settings before Spot Settings are enabled.
FUTURE_MIN_VOLUME_INVALID=FUTURE: Min Order Quantity cannot exceed Max Order Quantity.
FUTURE_MAX_PRICE_INVALID=FUTURE: Max Order Price must be greater than or equal to Min Order Price.
FUTURE_MIN_NOTIONAL_INVALID=FUTURE: Min Order Notional cannot exceed Max Order Notional.
SPOT_MIN_VOLUME_INVALID=SPOT: Min Order Quantity cannot exceed Max Order Quantity.
SPOT_MAX_PRICE_INVALID=SPOT: Max Order Price must be greater than or equal to Min Order Price.
SPOT_MIN_NOTIONAL_INVALID=SPOT: Min Order Notional cannot exceed Max Order Notional.
DISPLAY_NAME_EXISTS=Pair Display Name already exists.
SPOT_REQUIRED=Spot must be enabled first
TRADING_TYPE_NOT_SUPPORTED=Trading type [{0}] is not supported
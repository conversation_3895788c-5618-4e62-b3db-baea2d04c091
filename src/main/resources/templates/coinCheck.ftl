<html>
<head>
	<meta charset="UTF-8">
	<title>System Notification</title>
</head>
<style type="text/css">
	table.gridtable {
		font-family: verdana,arial,sans-serif;
		font-size:11px;
		color:#333333;
		border-width: 1px;
		border-color: #666666;
		border-collapse: collapse;
		width: 100%;
	}
	table.gridtable tr th {
		border-width: 1px;
		padding: 8px;
		border-style: solid;
		border-color: #666666;
		background-color: #dedede;
	}
	table.gridtable td {
		border-width: 1px !important;
		padding: 8px;
		border-style: solid !important;
		border-color: #666666 !important;
		background-color: #ffffff;
		width: 25%;
		text-align: center;
	}
</style>
<body>
<h3 style="text-align:center;">Currency RPC interface health check report</h3>
<hr>
<div>
	<h4 style="color:#FF0000;">Coins to be noted:</h4>
	<table class="gridtable">
		<tr><th>Currency</th><th>Account Type</th><th>RPC</th><th>Block Height</th></tr>
		<#list noticeList as coin>
			<tr>
				<td style="color:#FF0000;">${coin.unit}</td>
				<#if coin.accountType==0>
					<td>Address Mode</td>
				<#else>
					<td style="color: rgb(69, 184, 84);">Account mode</td>
				</#if>
				<#if coin.enableRpc=="IS_TRUE">
					<td style="color: rgb(69, 184, 84);">Open</td>
				<#else>
					<td style="color: #FF0000;">Close</td>
				</#if>
				<td style="color:#FF0000;">${coin.blockHeight}</td>
			</tr>
		</#list>
	</table>
</div>
<br/><br/>
<div>
	<h4>Block height change currency:</h4>
	<table class="gridtable">
		<tr><th>Currency</th><th>Account Type</th><th>RPC</th><th>Block Height</th></tr>
		<#list changeList as coin>
			<tr>
				<td>${coin.unit}</td>
				<#if coin.accountType==0>
					<td>Address Mode</td>
				<#else>
					<td style="color: rgb(69, 184, 84);">Account mode</td>
				</#if>


				<#if coin.enableRpc=="IS_TRUE">
					<td style="color: rgb(69, 184, 84);">Open</td>
				<#else>
					<td style="color: #FF0000;">Close</td>
				</#if>
				<td>${coin.blockHeight}</td>
			</tr>
		</#list>
	</table>
</div>
<br/><br/>
<div>
	<h4>Block height has not changed Currency:</h4>
	<table class="gridtable">
		<tr><th>Currency</th><th>Account Type</th><th>RPC</th><th>Block Height</th></tr>
		<#list nochangeList as coin>
			<tr>
				<td>${coin.unit}</td>
				<#if coin.accountType==0>
					<td>Address Mode</td>
				<#else>
					<td style="color: rgb(69, 184, 84);">Account mode</td>
				</#if>


				<#if coin.enableRpc=="IS_TRUE">
					<td style="color: rgb(69, 184, 84);">Open</td>
				<#else>
					<td style="color: #FF0000;">Close</td>
				</#if>
				<td>${coin.blockHeight}</td>
			</tr>
		</#list>
	</table>
</div>
</body>
</html>